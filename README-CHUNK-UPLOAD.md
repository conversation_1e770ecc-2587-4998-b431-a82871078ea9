# 林班大文件分片上传功能

## 功能概述

本次扩展为林班系统添加了强大的大文件上传功能，支持：

- 🚀 **大文件分片上传** - 支持GB级别大文件上传
- 🔄 **断点续传** - 网络中断后可继续上传
- ⏸️ **暂停/恢复** - 用户可控制上传过程
- 📊 **实时进度** - 查看详细上传进度
- 🔒 **文件完整性** - MD5校验确保文件完整
- 🏷️ **林班元数据** - 支持林班、时间、采集人员信息
- 🔐 **可选鉴权** - 支持imToken鉴权或无鉴权访问

## 技术架构

### 数据库设计

1. **linban_chunk_upload** - 分片上传主表
   - 存储上传任务的基本信息和进度
   - 包含林班元数据字段

2. **linban_chunk_detail** - 分片详情表
   - 存储每个分片的上传状态
   - 支持重试机制

### 核心组件

1. **ChunkUploadService** - 分片上传服务
2. **AppChunkUploadController** - REST API控制器
3. **ImTokenAuthInterceptor** - 可选的鉴权拦截器
4. **ChunkUploadCleanupJob** - 定时清理任务

## 部署步骤

### 1. 数据库初始化

执行SQL脚本创建相关表：

```bash
mysql -u root -p your_database < sql/mysql/linban_chunk_upload.sql
```

### 2. 配置文件上传大小

在 `application.yaml` 中确保配置了足够的文件上传大小：

```yaml
spring:
  servlet:
    multipart:
      max-file-size: 100MB  # 单个分片大小限制
      max-request-size: 100MB  # 请求总大小限制
```

### 3. 配置定时任务（可选）

在系统管理后台添加定时任务：

- **任务名称**: 分片上传清理任务
- **任务组**: linban
- **调用目标**: chunkUploadCleanupJob.execute
- **cron表达式**: `0 0 2 * * ?` (每天凌晨2点执行)

## API使用指南

### 基本上传流程

```javascript
// 1. 计算文件MD5
const fileMd5 = await calculateMD5(file);

// 2. 初始化上传
const initResp = await fetch('/app-api/linban/upload/init', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    fileName: file.name,
    totalSize: file.size,
    chunkSize: 5 * 1024 * 1024, // 5MB分片
    fileMd5: fileMd5,
    fileType: file.type,
    linbanId: 1,
    linbanName: '东山林班',
    collectTime: new Date().toISOString(),
    collector: '张三'
  })
});

const { uploadId, totalChunks, isResume, uploadedChunks } = initResp.data;

// 3. 上传分片（跳过已上传的分片）
for (let i = 0; i < totalChunks; i++) {
  if (isResume && uploadedChunks.includes(i)) {
    continue; // 跳过已上传的分片
  }
  
  const start = i * chunkSize;
  const end = Math.min(start + chunkSize, file.size);
  const chunk = file.slice(start, end);
  const chunkMd5 = await calculateMD5(chunk);
  
  const formData = new FormData();
  formData.append('uploadId', uploadId);
  formData.append('chunkIndex', i);
  formData.append('chunkFile', chunk);
  formData.append('chunkMd5', chunkMd5);
  
  const uploadResp = await fetch('/app-api/linban/upload/chunk', {
    method: 'POST',
    body: formData
  });
  
  // 更新进度
  const { progress } = uploadResp.data;
  updateProgressBar(progress);
}

// 4. 完成上传
const completeResp = await fetch('/app-api/linban/upload/complete', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ uploadId })
});

const { finalFileUrl } = completeResp.data;
console.log('文件上传完成:', finalFileUrl);
```

### 暂停和恢复

```javascript
// 暂停上传
await fetch(`/app-api/linban/upload/pause?uploadId=${uploadId}`, {
  method: 'POST'
});

// 恢复上传
await fetch(`/app-api/linban/upload/resume?uploadId=${uploadId}`, {
  method: 'POST'
});

// 查询进度
const progressResp = await fetch(`/app-api/linban/upload/progress?uploadId=${uploadId}`);
const { progress, status } = progressResp.data;
```

### 使用imToken鉴权

```javascript
// 先登录获取token
const loginResp = await fetch('/app-api/linban/user/login-im', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    phoneNumber: '13800138000'
  })
});

const { imToken } = loginResp.data;

// 在上传请求中使用token
const initResp = await fetch('/app-api/linban/upload/init', {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'Im-Token': imToken  // 添加鉴权头
  },
  body: JSON.stringify({
    // ... 其他参数
    imToken: imToken  // 也可以在body中传递
  })
});
```

## 配置说明

### 临时文件存储

默认临时文件存储在系统临时目录：`/tmp/linban-upload/`

可以通过修改 `ChunkUploadServiceImpl.TEMP_UPLOAD_PATH` 常量来自定义路径。

### 清理策略

- 自动清理7天前的过期上传任务
- 清理包括数据库记录和临时文件
- 可通过定时任务配置清理频率

### 性能优化建议

1. **分片大小**: 建议设置为1-10MB，根据网络环境调整
2. **并发控制**: 前端可实现并发上传多个分片
3. **重试机制**: 分片上传失败时可重试
4. **进度缓存**: 可将进度信息缓存到本地存储

## 错误处理

常见错误码：

- `100_100_200`: 上传任务不存在
- `100_100_201`: 分片MD5校验失败
- `100_100_202`: 分片上传失败
- `100_100_203`: 文件上传未完成，无法合并
- `100_100_204`: 创建临时目录失败

## 监控和运维

### 日志监控

关键日志位置：
- 分片上传: `ChunkUploadServiceImpl`
- 文件合并: `mergeChunkFiles`
- 清理任务: `ChunkUploadCleanupJob`

### 存储监控

定期检查：
- 临时目录磁盘使用情况
- 数据库表大小
- 过期任务数量

## 扩展功能

### 支持的扩展

1. **多存储后端**: 可扩展支持阿里云OSS、腾讯云COS等
2. **压缩上传**: 可在分片前进行压缩
3. **加密上传**: 可对敏感文件进行加密
4. **秒传功能**: 基于MD5实现文件秒传
5. **上传统计**: 添加上传速度、成功率等统计

### 自定义配置

可通过配置文件自定义：
- 分片大小限制
- 并发上传数量
- 临时文件保留时间
- 支持的文件类型

## 故障排除

### 常见问题

1. **上传失败**: 检查网络连接和服务器磁盘空间
2. **进度不更新**: 检查数据库连接和事务配置
3. **文件损坏**: 检查MD5校验逻辑
4. **临时文件堆积**: 检查清理任务是否正常运行

### 调试技巧

1. 开启DEBUG日志查看详细信息
2. 检查临时目录文件状态
3. 查询数据库表了解上传状态
4. 使用API测试工具验证接口

## 联系支持

如有问题，请联系开发团队或查看项目文档。

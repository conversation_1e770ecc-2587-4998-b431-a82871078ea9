package cn.iocoder.yudao.module.linban.service.upload;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.linban.controller.app.upload.vo.ChunkUploadInitReqVO;
import cn.iocoder.yudao.module.linban.controller.app.upload.vo.ChunkUploadInitRespVO;
import cn.iocoder.yudao.module.linban.dal.mysql.upload.ChunkDetailMapper;
import cn.iocoder.yudao.module.linban.dal.mysql.upload.ChunkUploadMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link ChunkUploadService} 的单元测试类
 *
 * <AUTHOR>
 */
@Import({ChunkUploadServiceImpl.class})
public class ChunkUploadServiceTest extends BaseDbUnitTest {

    @Resource
    private ChunkUploadService chunkUploadService;

    @Resource
    private ChunkUploadMapper chunkUploadMapper;

    @Resource
    private ChunkDetailMapper chunkDetailMapper;

    @Test
    public void testInitChunkUpload_success() {
        // 准备参数
        ChunkUploadInitReqVO reqVO = new ChunkUploadInitReqVO();
        reqVO.setFileName("test.mp4");
        reqVO.setTotalSize(1024 * 1024 * 100L); // 100MB
        reqVO.setChunkSize(1024 * 1024 * 5L); // 5MB
        reqVO.setFileMd5("d41d8cd98f00b204e9800998ecf8427e");
        reqVO.setFileType("video/mp4");
        reqVO.setLinbanId(1L);
        reqVO.setLinbanName("测试林班");
        reqVO.setCollectTime(LocalDateTime.now());
        reqVO.setCollector("测试采集员");

        // 调用
        ChunkUploadInitRespVO respVO = chunkUploadService.initChunkUpload(reqVO);

        // 断言
        assertNotNull(respVO);
        assertNotNull(respVO.getUploadId());
        assertEquals(20, respVO.getTotalChunks()); // 100MB / 5MB = 20
        assertEquals(0, respVO.getUploadedChunks().size());
        assertFalse(respVO.getIsResume());
        assertEquals(0.0, respVO.getProgress());
    }

    @Test
    public void testInitChunkUpload_resume() {
        // TODO: 实现断点续传测试
    }

    @Test
    public void testUploadChunk_success() {
        // TODO: 实现分片上传测试
    }

    @Test
    public void testCompleteChunkUpload_success() {
        // TODO: 实现完成上传测试
    }
}

package cn.iocoder.yudao.module.linban.controller.app.upload.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "用户App - 分片上传分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChunkUploadPageReqVO extends PageParam {

    @Schema(description = "上传任务ID")
    private String uploadId;

    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "上传状态")
    private Integer status;

    @Schema(description = "所属林班ID")
    private Long linbanId;

    @Schema(description = "采集人员")
    private String collector;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}

package cn.iocoder.yudao.module.linban.controller.app.upload.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "用户App - 上传进度查询 Response VO")
@Data
public class ChunkUploadProgressRespVO {

    @Schema(description = "上传任务ID")
    private String uploadId;

    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "文件总大小")
    private Long totalSize;

    @Schema(description = "上传状态：0-初始化，1-上传中，2-暂停，3-完成，4-失败")
    private Integer status;

    @Schema(description = "上传进度百分比")
    private Double progress;

    @Schema(description = "已上传分片数")
    private Integer uploadedChunks;

    @Schema(description = "总分片数")
    private Integer totalChunks;

    @Schema(description = "所属林班名称")
    private String linbanName;

    @Schema(description = "采集人员")
    private String collector;

    @Schema(description = "采集时间")
    private LocalDateTime collectTime;

    @Schema(description = "最终文件URL（完成时）")
    private String finalFileUrl;

    @Schema(description = "错误信息（失败时）")
    private String errorMessage;

    @Schema(description = "最后活跃时间")
    private LocalDateTime lastActiveTime;

    @Schema(description = "是否需要继续轮询此文件进度")
    private Boolean needContinuePolling;

    @Schema(description = "建议轮询间隔（毫秒）", example = "2000")
    private Long suggestedPollingInterval;

    @Schema(description = "轮询状态说明")
    private String pollingMessage;

}

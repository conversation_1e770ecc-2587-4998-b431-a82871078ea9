package cn.iocoder.yudao.module.linban.service.upload;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.linban.controller.app.upload.vo.*;
import cn.iocoder.yudao.module.linban.dal.dataobject.upload.ChunkUploadDO;

import javax.validation.Valid;

/**
 * 分片上传 Service 接口
 *
 * <AUTHOR>
 */
public interface ChunkUploadService {

    /**
     * 初始化分片上传
     *
     * @param createReqVO 创建信息
     * @return 初始化结果
     */
    ChunkUploadInitRespVO initChunkUpload(@Valid ChunkUploadInitReqVO createReqVO);

    /**
     * 上传分片
     *
     * @param uploadReqVO 上传信息
     * @return 上传结果
     */
    ChunkUploadRespVO uploadChunk(@Valid ChunkUploadReqVO uploadReqVO);

    /**
     * 完成分片上传
     *
     * @param completeReqVO 完成信息
     * @return 完成结果
     */
    ChunkUploadCompleteRespVO completeChunkUpload(@Valid ChunkUploadCompleteReqVO completeReqVO);

    /**
     * 暂停上传
     *
     * @param uploadId 上传任务ID
     */
    void pauseUpload(String uploadId);

    /**
     * 恢复上传
     *
     * @param uploadId 上传任务ID
     */
    void resumeUpload(String uploadId);

    /**
     * 取消上传
     *
     * @param uploadId 上传任务ID
     */
    void cancelUpload(String uploadId);

    /**
     * 获得上传进度
     *
     * @param uploadId 上传任务ID
     * @return 上传进度
     */
    ChunkUploadProgressRespVO getUploadProgress(String uploadId);

    /**
     * 获得分片上传分页
     *
     * @param pageReqVO 分页查询
     * @return 分片上传分页
     */
    PageResult<ChunkUploadDO> getChunkUploadPage(ChunkUploadPageReqVO pageReqVO);

    /**
     * 删除分片上传记录
     *
     * @param id 编号
     */
    void deleteChunkUpload(Long id);

    /**
     * 清理过期的上传任务
     */
    void cleanExpiredUploads();

}

package cn.iocoder.yudao.module.linban.dal.mysql.upload;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.linban.dal.dataobject.upload.ChunkDetailDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 分片详情 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ChunkDetailMapper extends BaseMapperX<ChunkDetailDO> {

    default List<ChunkDetailDO> selectByUploadId(String uploadId) {
        return selectList(new LambdaQueryWrapperX<ChunkDetailDO>()
                .eq(ChunkDetailDO::getUploadId, uploadId)
                .orderByAsc(ChunkDetailDO::getChunkIndex));
    }

    default ChunkDetailDO selectByUploadIdAndChunkIndex(String uploadId, Integer chunkIndex) {
        return selectOne(new LambdaQueryWrapperX<ChunkDetailDO>()
                .eq(ChunkDetailDO::getUploadId, uploadId)
                .eq(ChunkDetailDO::getChunkIndex, chunkIndex));
    }

    default List<ChunkDetailDO> selectUploadedChunks(String uploadId) {
        return selectList(new LambdaQueryWrapperX<ChunkDetailDO>()
                .eq(ChunkDetailDO::getUploadId, uploadId)
                .eq(ChunkDetailDO::getStatus, 2) // 上传成功
                .orderByAsc(ChunkDetailDO::getChunkIndex));
    }

    default int countUploadedChunks(String uploadId) {
        return selectCount(new LambdaQueryWrapperX<ChunkDetailDO>()
                .eq(ChunkDetailDO::getUploadId, uploadId)
                .eq(ChunkDetailDO::getStatus, 2)); // 上传成功
    }

    default void insertBatch(Collection<ChunkDetailDO> list) {
        // MyBatis-Plus 的批量插入方法
        for (ChunkDetailDO item : list) {
            insert(item);
        }
    }

}

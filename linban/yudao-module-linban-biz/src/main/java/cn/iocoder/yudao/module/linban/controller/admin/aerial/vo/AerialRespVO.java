package cn.iocoder.yudao.module.linban.controller.admin.aerial.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 林班-航拍数据集 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AerialRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "6580")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "航拍数据集网络地址", example = "https://www.iocoder.cn")
    @ExcelProperty("航拍数据集网络地址")
    private String url;

    private String name;

    private Long quartelId;

    private String time;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
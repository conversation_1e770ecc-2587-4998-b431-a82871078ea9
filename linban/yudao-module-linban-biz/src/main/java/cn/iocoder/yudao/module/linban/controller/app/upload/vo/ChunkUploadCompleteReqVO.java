package cn.iocoder.yudao.module.linban.controller.app.upload.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Schema(description = "用户App - 完成分片上传 Request VO")
@Data
public class ChunkUploadCompleteReqVO {

    @Schema(description = "上传任务ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "上传任务ID不能为空")
    private String uploadId;

}

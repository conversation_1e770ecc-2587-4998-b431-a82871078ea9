package cn.iocoder.yudao.module.linban.dal.mongo.friend;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FriendRepository extends MongoRepository<Friend, String> {
    List<Friend> findByOwnerUserId(String ownerUserId);

    // 用于快速判断是否存在
    boolean existsByOwnerUserIdAndFriendUserId(Long ownerUserId, Long friendUserId);
}
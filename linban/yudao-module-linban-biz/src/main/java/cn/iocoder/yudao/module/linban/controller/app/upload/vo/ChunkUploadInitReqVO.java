package cn.iocoder.yudao.module.linban.controller.app.upload.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "用户App - 初始化分片上传 Request VO")
@Data
public class ChunkUploadInitReqVO {

    @Schema(description = "文件名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "文件名不能为空")
    private String fileName;

    @Schema(description = "文件总大小（字节）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "文件大小不能为空")
    private Long totalSize;

    @Schema(description = "分片大小（字节）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分片大小不能为空")
    private Long chunkSize;

    @Schema(description = "文件MD5值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "文件MD5不能为空")
    private String fileMd5;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "所属林班ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "所属林班不能为空")
    private Long linbanId;

    @Schema(description = "所属林班名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "林班名称不能为空")
    private String linbanName;

    @Schema(description = "采集时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "采集时间不能为空")
    private LocalDateTime collectTime;

    @Schema(description = "采集人员", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "采集人员不能为空")
    private String collector;

    @Schema(description = "用户imToken（可选，用于鉴权）")
    private String imToken;

}

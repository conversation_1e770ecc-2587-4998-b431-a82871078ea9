package cn.iocoder.yudao.module.linban.controller.app.upload;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.linban.controller.app.upload.vo.*;
import cn.iocoder.yudao.module.linban.dal.dataobject.upload.ChunkUploadDO;
import cn.iocoder.yudao.module.linban.service.upload.ChunkUploadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户App - 分片上传")
@RestController
@RequestMapping("/app-api/linban/upload")
@Validated
@Slf4j
public class AppChunkUploadController {

    @Resource
    private ChunkUploadService chunkUploadService;

    @PostMapping("/init")
    @Operation(summary = "初始化分片上传")
    @PermitAll
    public CommonResult<ChunkUploadInitRespVO> initChunkUpload(@Valid @RequestBody ChunkUploadInitReqVO createReqVO) {
        return success(chunkUploadService.initChunkUpload(createReqVO));
    }

    @PostMapping("/chunk")
    @Operation(summary = "上传分片")
    @PermitAll
    public CommonResult<ChunkUploadRespVO> uploadChunk(@Valid ChunkUploadReqVO uploadReqVO) {
        return success(chunkUploadService.uploadChunk(uploadReqVO));
    }

    @PostMapping("/complete")
    @Operation(summary = "完成分片上传")
    @PermitAll
    public CommonResult<ChunkUploadCompleteRespVO> completeChunkUpload(@Valid @RequestBody ChunkUploadCompleteReqVO completeReqVO) {
        return success(chunkUploadService.completeChunkUpload(completeReqVO));
    }

    @PostMapping("/pause")
    @Operation(summary = "暂停上传")
    @PermitAll
    public CommonResult<Boolean> pauseUpload(@RequestParam("uploadId") String uploadId) {
        chunkUploadService.pauseUpload(uploadId);
        return success(true);
    }

    @PostMapping("/resume")
    @Operation(summary = "恢复上传")
    @PermitAll
    public CommonResult<Boolean> resumeUpload(@RequestParam("uploadId") String uploadId) {
        chunkUploadService.resumeUpload(uploadId);
        return success(true);
    }

    @DeleteMapping("/cancel")
    @Operation(summary = "取消上传")
    @PermitAll
    public CommonResult<Boolean> cancelUpload(@RequestParam("uploadId") String uploadId) {
        chunkUploadService.cancelUpload(uploadId);
        return success(true);
    }

    @GetMapping("/progress")
    @Operation(summary = "获取上传进度")
    @PermitAll
    public CommonResult<ChunkUploadProgressRespVO> getUploadProgress(@RequestParam("uploadId") String uploadId) {
        return success(chunkUploadService.getUploadProgress(uploadId));
    }

    @GetMapping("/page")
    @Operation(summary = "获得分片上传分页")
    @PermitAll
    public CommonResult<PageResult<ChunkUploadDO>> getChunkUploadPage(@Valid ChunkUploadPageReqVO pageReqVO) {
        PageResult<ChunkUploadDO> pageResult = chunkUploadService.getChunkUploadPage(pageReqVO);
        return success(pageResult);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除分片上传记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    public CommonResult<Boolean> deleteChunkUpload(@RequestParam("id") Long id) {
        chunkUploadService.deleteChunkUpload(id);
        return success(true);
    }

}

package cn.iocoder.yudao.module.linban.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

public interface ErrorCodeConstants {
    ErrorCode AERIAL_NOT_EXISTS = new ErrorCode(100_100_100, "林班-航拍数据集不存在");

    ErrorCode JOB_NOT_EXISTS = new ErrorCode(100_100_101, "林班-工作不存在");


    ErrorCode JOB_GROUP_NOT_EXISTS = new ErrorCode(100_100_102, "林班-工作分类不存在");

    ErrorCode LABEL_NOT_EXISTS = new ErrorCode(100_100_103, "林班-标记点不存在");

    ErrorCode LABEL_FOLDER_NOT_EXISTS = new ErrorCode(100_100_104, "林班-标记点-收藏夹关联不存在");

    ErrorCode LINBAN_NOT_EXISTS = new ErrorCode(100_100_105, "林班不存在");

    ErrorCode LLINBAN_TAG_NOT_EXISTS = new ErrorCode(100_100_106, "林班-标签不存在");

    ErrorCode START_FOLDER_NOT_EXISTS = new ErrorCode(100_100_107, "林班-收藏夹不存在");

    ErrorCode USER_TRACK_NOT_EXISTS = new ErrorCode(100_100_108, "林班-用户跟踪轨迹不存在");

    // ========== 分片上传相关错误码 100_100_200 ==========
    ErrorCode UPLOAD_TASK_NOT_FOUND = new ErrorCode(100_100_200, "上传任务不存在");
    ErrorCode CHUNK_MD5_MISMATCH = new ErrorCode(100_100_201, "分片MD5校验失败");
    ErrorCode CHUNK_UPLOAD_FAILED = new ErrorCode(100_100_202, "分片上传失败");
    ErrorCode UPLOAD_NOT_COMPLETE = new ErrorCode(100_100_203, "文件上传未完成，无法合并");
    ErrorCode CREATE_TEMP_DIR_FAILED = new ErrorCode(100_100_204, "创建临时目录失败");
    ErrorCode MERGE_CHUNKS_FAILED = new ErrorCode(100_100_205, "合并分片文件失败");
    ErrorCode FILE_MD5_MISMATCH = new ErrorCode(100_100_206, "文件MD5校验失败");
    ErrorCode UPLOAD_TO_STORAGE_FAILED = new ErrorCode(100_100_207, "上传到存储系统失败");

}
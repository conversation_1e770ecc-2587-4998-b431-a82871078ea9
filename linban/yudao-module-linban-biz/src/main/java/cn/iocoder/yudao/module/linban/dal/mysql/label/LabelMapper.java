package cn.iocoder.yudao.module.linban.dal.mysql.label;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.linban.dal.dataobject.label.LabelDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.linban.controller.admin.label.vo.*;

/**
 * 林班-标记点 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LabelMapper extends BaseMapperX<LabelDO> {

    default PageResult<LabelDO> selectPage(LabelPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LabelDO>()
                .inIfPresent(LabelDO::getId , reqVO.getIds())
                .likeIfPresent(LabelDO::getLabelName, reqVO.getLabelName())
                .eqIfPresent(LabelDO::getLabelRemark, reqVO.getLabelRemark())
                .eqIfPresent(LabelDO::getUserId, reqVO.getUserId())
                .eqIfPresent(LabelDO::getQuartelId, reqVO.getQuartelId())
                .betweenIfPresent(LabelDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(LabelDO::getId));
    }

}
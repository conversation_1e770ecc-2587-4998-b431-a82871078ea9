package cn.iocoder.yudao.module.linban.service.llinbantag;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.linban.controller.admin.llinbantag.vo.*;
import cn.iocoder.yudao.module.linban.dal.dataobject.llinbantag.LlinbanTagDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.linban.dal.mysql.llinbantag.LlinbanTagMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static  cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.*;
/**
 * 林班-标签 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LlinbanTagServiceImpl extends ServiceImpl<LlinbanTagMapper , LlinbanTagDO> implements LlinbanTagService {

    @Resource
    private LlinbanTagMapper llinbanTagMapper;

    @Override
    public Long createLlinbanTag(LlinbanTagSaveReqVO createReqVO) {
        // 插入
        LlinbanTagDO llinbanTag = BeanUtils.toBean(createReqVO, LlinbanTagDO.class);
        llinbanTagMapper.insert(llinbanTag);
        // 返回
        return llinbanTag.getId();
    }

    @Override
    public void updateLlinbanTag(LlinbanTagSaveReqVO updateReqVO) {
        // 校验存在
        validateLlinbanTagExists(updateReqVO.getId());
        // 更新
        LlinbanTagDO updateObj = BeanUtils.toBean(updateReqVO, LlinbanTagDO.class);
        llinbanTagMapper.updateById(updateObj);
    }

    @Override
    public void deleteLlinbanTag(Long id) {
        // 校验存在
        validateLlinbanTagExists(id);
        // 删除
        llinbanTagMapper.deleteById(id);
    }

    private void validateLlinbanTagExists(Long id) {
        if (llinbanTagMapper.selectById(id) == null) {
            throw exception(LLINBAN_TAG_NOT_EXISTS);
        }
    }

    @Override
    public LlinbanTagDO getLlinbanTag(Long id) {
        return llinbanTagMapper.selectById(id);
    }

    @Override
    public PageResult<LlinbanTagDO> getLlinbanTagPage(LlinbanTagPageReqVO pageReqVO) {
        return llinbanTagMapper.selectPage(pageReqVO);
    }

}
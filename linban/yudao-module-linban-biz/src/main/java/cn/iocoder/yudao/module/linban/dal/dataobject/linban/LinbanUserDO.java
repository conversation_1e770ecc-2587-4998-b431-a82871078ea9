package cn.iocoder.yudao.module.linban.dal.dataobject.linban;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("linban_user_relation")
public class LinbanUserDO {

    private Long id;

    private Long userId;

    private Long linbanId;

    public LinbanUserDO(Long userId, Long linbanId) {
        this.userId = userId;
        this.linbanId = linbanId;
    }
}

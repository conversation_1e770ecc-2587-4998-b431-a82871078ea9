package cn.iocoder.yudao.module.linban.dal.mysql.upload;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.linban.controller.app.upload.vo.ChunkUploadPageReqVO;
import cn.iocoder.yudao.module.linban.dal.dataobject.upload.ChunkUploadDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 分片上传记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ChunkUploadMapper extends BaseMapperX<ChunkUploadDO> {

    default PageResult<ChunkUploadDO> selectPage(ChunkUploadPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ChunkUploadDO>()
                .eqIfPresent(ChunkUploadDO::getUploadId, reqVO.getUploadId())
                .likeIfPresent(ChunkUploadDO::getFileName, reqVO.getFileName())
                .eqIfPresent(ChunkUploadDO::getStatus, reqVO.getStatus())
                .eqIfPresent(ChunkUploadDO::getLinbanId, reqVO.getLinbanId())
                .likeIfPresent(ChunkUploadDO::getCollector, reqVO.getCollector())
                .betweenIfPresent(ChunkUploadDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ChunkUploadDO::getId));
    }

    default ChunkUploadDO selectByUploadId(String uploadId) {
        return selectOne(ChunkUploadDO::getUploadId, uploadId);
    }

}

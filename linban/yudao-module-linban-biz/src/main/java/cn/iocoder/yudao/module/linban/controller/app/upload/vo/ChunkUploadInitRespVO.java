package cn.iocoder.yudao.module.linban.controller.app.upload.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "用户App - 初始化分片上传 Response VO")
@Data
public class ChunkUploadInitRespVO {

    @Schema(description = "上传任务ID")
    private String uploadId;

    @Schema(description = "总分片数")
    private Integer totalChunks;

    @Schema(description = "已上传的分片索引列表")
    private List<Integer> uploadedChunks;

    @Schema(description = "是否为断点续传")
    private Boolean isResume;

    @Schema(description = "当前上传进度")
    private Double progress;

}

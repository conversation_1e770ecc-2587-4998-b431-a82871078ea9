package cn.iocoder.yudao.module.linban.framework.security;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.module.linban.dal.dataobject.user.LinbanUser;
import cn.iocoder.yudao.module.linban.service.user.LinBanUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * ImToken 认证拦截器
 * 用于验证林班系统的 imToken
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ImTokenAuthInterceptor implements HandlerInterceptor {

    @Resource
    private LinBanUserService linBanUserService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取 imToken
        String imToken = getImToken(request);
        
        // 如果没有 imToken，则跳过验证（允许无鉴权访问）
        if (StrUtil.isEmpty(imToken)) {
            return true;
        }

        // 验证 imToken
        if (!validateImToken(imToken)) {
            throw new ServiceException(GlobalErrorCodeConstants.UNAUTHORIZED);
        }

        return true;
    }

    /**
     * 从请求中获取 imToken
     */
    private String getImToken(HttpServletRequest request) {
        // 优先从 Header 中获取
        String imToken = request.getHeader("Im-Token");
        if (StrUtil.isNotEmpty(imToken)) {
            return imToken;
        }

        // 从参数中获取
        imToken = request.getParameter("imToken");
        if (StrUtil.isNotEmpty(imToken)) {
            return imToken;
        }

        return null;
    }

    /**
     * 验证 imToken 是否有效
     */
    private boolean validateImToken(String imToken) {
        try {
            // 这里可以根据实际需求实现 imToken 验证逻辑
            // 目前简单验证是否存在对应的用户
            LinbanUser user = linBanUserService.lambdaQuery()
                    .eq(LinbanUser::getImUserId, imToken)
                    .one();
            
            return user != null;
        } catch (Exception e) {
            log.error("验证 imToken 失败", e);
            return false;
        }
    }
}

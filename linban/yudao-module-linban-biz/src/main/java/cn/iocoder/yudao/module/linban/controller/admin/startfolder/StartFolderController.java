package cn.iocoder.yudao.module.linban.controller.admin.startfolder;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.linban.controller.admin.startfolder.vo.*;
import cn.iocoder.yudao.module.linban.dal.dataobject.startfolder.StartFolderDO;
import cn.iocoder.yudao.module.linban.service.startfolder.StartFolderService;

@Tag(name = "管理后台 - 林班-收藏夹")
@RestController
@RequestMapping("/linban/start-folder")
@Validated
public class StartFolderController {

    @Resource
    private StartFolderService startFolderService;

    @PostMapping("/create")
    @Operation(summary = "创建林班-收藏夹")
    @PreAuthorize("@ss.hasPermission('linban:start-folder:create')")
    public CommonResult<Long> createStartFolder(@Valid @RequestBody StartFolderSaveReqVO createReqVO) {
        return success(startFolderService.createStartFolder(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新林班-收藏夹")
    @PreAuthorize("@ss.hasPermission('linban:start-folder:update')")
    public CommonResult<Boolean> updateStartFolder(@Valid @RequestBody StartFolderSaveReqVO updateReqVO) {
        startFolderService.updateStartFolder(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除林班-收藏夹")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('linban:start-folder:delete')")
    public CommonResult<Boolean> deleteStartFolder(@RequestParam("id") Long id) {
        startFolderService.deleteStartFolder(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得林班-收藏夹")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('linban:start-folder:query')")
    public CommonResult<StartFolderRespVO> getStartFolder(@RequestParam("id") Long id) {
        StartFolderDO startFolder = startFolderService.getStartFolder(id);
        return success(BeanUtils.toBean(startFolder, StartFolderRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得林班-收藏夹分页")
    @PreAuthorize("@ss.hasPermission('linban:start-folder:query')")
    public CommonResult<PageResult<StartFolderRespVO>> getStartFolderPage(@Valid StartFolderPageReqVO pageReqVO) {
        PageResult<StartFolderDO> pageResult = startFolderService.getStartFolderPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StartFolderRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出林班-收藏夹 Excel")
    @PreAuthorize("@ss.hasPermission('linban:start-folder:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportStartFolderExcel(@Valid StartFolderPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<StartFolderDO> list = startFolderService.getStartFolderPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "林班-收藏夹.xls", "数据", StartFolderRespVO.class,
                        BeanUtils.toBean(list, StartFolderRespVO.class));
    }

}
package cn.iocoder.yudao.module.linban.utils;

// CustomGeoJsonTypeHandler.java
import cn.iocoder.yudao.module.linban.controller.admin.label.vo.DataJsonVO;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.BasicPolymorphicTypeValidator;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;

// CustomGeoJsonTypeHandler.java
@Component
@MappedTypes(DataJsonVO.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class CustomGeoJsonTypeHandler extends JacksonTypeHandler {

    private static final ObjectMapper GEO_JSON_MAPPER = createGeoJsonMapper();

    public CustomGeoJsonTypeHandler() {
        super(DataJsonVO.class);
        setObjectMapper(GEO_JSON_MAPPER);
        System.out.println("注册了json");
    }

    private static ObjectMapper createGeoJsonMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return mapper;
    }

    @Override
    public Object getResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        try {
            return GEO_JSON_MAPPER.readValue(json, DataJsonVO.class);
        } catch (Exception e) {
            System.err.println("GeoJSON 反序列化失败: " + e.getMessage());
            return null;
        }
    }
}
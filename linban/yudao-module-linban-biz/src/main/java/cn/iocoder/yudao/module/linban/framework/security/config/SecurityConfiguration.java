package cn.iocoder.yudao.module.linban.framework.security.config;

import cn.iocoder.yudao.framework.security.config.AuthorizeRequestsCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer;

/**
 * 林班模块的 Security 配置
 */
@Configuration("linbanSecurityConfiguration")
public class SecurityConfiguration {

    @Bean("linbanAuthorizeRequestsCustomizer")
    public AuthorizeRequestsCustomizer authorizeRequestsCustomizer() {
        return new AuthorizeRequestsCustomizer() {

            @Override
            public void customize(AuthorizeHttpRequestsConfigurer<HttpSecurity>.AuthorizationManagerRequestMatcherRegistry registry) {
                // 林班所有接口，允许匿名访问（无需鉴权）
                registry.requestMatchers(buildAppApi("/linban/**")).permitAll();
                // 也可以直接指定路径
                registry.requestMatchers("/app-api/linban/**").permitAll();
            }

            @Override
            public int getOrder() {
                return -1; // 确保优先级高于其他配置
            }

        };
    }

}

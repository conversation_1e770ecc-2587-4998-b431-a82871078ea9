package cn.iocoder.yudao.module.linban.framework.security.config;

import cn.iocoder.yudao.framework.security.config.AuthorizeRequestsCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer;

/**
 * 林班模块的 Security 配置
 */
@Configuration("linbanSecurityConfiguration")
public class SecurityConfiguration {

    @Bean("linbanAuthorizeRequestsCustomizer")
    public AuthorizeRequestsCustomizer authorizeRequestsCustomizer() {
        return new AuthorizeRequestsCustomizer() {

            @Override
            public void customize(AuthorizeHttpRequestsConfigurer<HttpSecurity>.AuthorizationManagerRequestMatcherRegistry registry) {
                // 林班分片上传接口，允许匿名访问
                registry.requestMatchers(buildAppApi("/linban/upload/**")).permitAll();
                // 林班用户登录接口，允许匿名访问
                registry.requestMatchers(buildAppApi("/linban/user/login-im")).permitAll();
                // 其他林班接口可以根据需要添加
            }

        };
    }

}

package cn.iocoder.yudao.module.linban.controller.app.upload.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户App - 完成分片上传 Response VO")
@Data
public class ChunkUploadCompleteRespVO {

    @Schema(description = "上传任务ID")
    private String uploadId;

    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "最终文件URL")
    private String finalFileUrl;

    @Schema(description = "文件大小")
    private Long fileSize;

    @Schema(description = "是否成功")
    private Boolean success;

    @Schema(description = "错误信息")
    private String errorMessage;

}

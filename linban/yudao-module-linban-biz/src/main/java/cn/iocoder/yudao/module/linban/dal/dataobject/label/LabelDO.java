package cn.iocoder.yudao.module.linban.dal.dataobject.label;

import cn.iocoder.yudao.module.linban.controller.admin.label.vo.DataJsonVO;
import cn.iocoder.yudao.module.linban.utils.CustomGeoJsonTypeHandler;
import cn.iocoder.yudao.module.linban.utils.GeoJsonDeserializer;
import cn.iocoder.yudao.module.linban.utils.GeoJsonSerializer;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 林班-标记点 DO
 *
 * <AUTHOR>
 */
@TableName("linban_label")
@KeySequence("linban_label_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LabelDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;

    private Long quartelId;


    private String type;

    private String startType;
    /**
     * 标记点名称
     */
    private String labelName;
    /**
     * 标记点备注
     */
    private String labelRemark;
    /**
     * geojson
     */
//    @JsonSerialize(using = GeoJsonSerializer.class) // 可选：自定义序列化
//    @JsonDeserialize(using = GeoJsonDeserializer.class) // 可选：自定义反序列化
    @TableField(typeHandler = CustomGeoJsonTypeHandler.class)
    private DataJsonVO dataJson;
    /**
     * 用户编号
     */
    private Long userId;

}
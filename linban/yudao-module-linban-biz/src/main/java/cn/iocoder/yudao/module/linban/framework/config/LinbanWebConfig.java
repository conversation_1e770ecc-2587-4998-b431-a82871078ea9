package cn.iocoder.yudao.module.linban.framework.config;

import cn.iocoder.yudao.module.linban.framework.security.ImTokenAuthInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * 林班模块 Web 配置
 *
 * <AUTHOR>
 */
@Configuration
public class LinbanWebConfig implements WebMvcConfigurer {

    @Resource
    private ImTokenAuthInterceptor imTokenAuthInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 林班模块所有接口都无需鉴权，暂时不添加任何拦截器
        // 如果将来需要鉴权，可以在这里添加 imToken 认证拦截器
    }
}

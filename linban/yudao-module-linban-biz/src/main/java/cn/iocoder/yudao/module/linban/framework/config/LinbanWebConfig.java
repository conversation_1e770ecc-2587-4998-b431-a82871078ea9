package cn.iocoder.yudao.module.linban.framework.config;

import cn.iocoder.yudao.module.linban.framework.security.ImTokenAuthInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * 林班模块 Web 配置
 *
 * <AUTHOR>
 */
@Configuration
public class LinbanWebConfig implements WebMvcConfigurer {

    @Resource
    private ImTokenAuthInterceptor imTokenAuthInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 imToken 认证拦截器
        registry.addInterceptor(imTokenAuthInterceptor)
                .addPathPatterns("/app-api/linban/**")
                .excludePathPatterns(
                        "/app-api/linban/user/login-im", // 排除登录接口
                        "/app-api/linban/upload/**" // 排除上传接口（因为已经在接口中处理了可选鉴权）
                );
    }
}

package cn.iocoder.yudao.module.linban.service.linban;

import java.util.*;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import cn.iocoder.yudao.module.linban.controller.admin.linban.vo.*;
import cn.iocoder.yudao.module.linban.dal.dataobject.linban.LinbanDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 林班 Service 接口
 *
 * <AUTHOR>
 */
public interface LinbanService extends IService<LinbanDO> {

    /**
     * 创建林班
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLinban(@Valid LinbanSaveReqVO createReqVO);

    /**
     * 更新林班
     *
     * @param updateReqVO 更新信息
     */
    void updateLinban(@Valid LinbanSaveReqVO updateReqVO);

    /**
     * 删除林班
     *
     * @param id 编号
     */
    void deleteLinban(Long id);

    /**
     * 获得林班
     *
     * @param id 编号
     * @return 林班
     */
    LinbanDO getLinban(Long id);

    /**
     * 获得林班分页
     *
     * @param pageReqVO 分页查询
     * @return 林班分页
     */
    PageResult<LinbanDO> getLinbanPage(LinbanPageReqVO pageReqVO);

}
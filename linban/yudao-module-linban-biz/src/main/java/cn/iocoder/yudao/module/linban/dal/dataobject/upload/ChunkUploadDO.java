package cn.iocoder.yudao.module.linban.dal.dataobject.upload;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 分片上传记录表
 * 用于支持大文件分片上传和断点续传
 *
 * <AUTHOR>
 */
@TableName("linban_chunk_upload")
@KeySequence("linban_chunk_upload_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChunkUploadDO extends BaseDO {

    /**
     * 编号，数据库自增
     */
    private Long id;

    /**
     * 上传任务ID，用于标识一次完整的文件上传
     */
    private String uploadId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件总大小（字节）
     */
    private Long totalSize;

    /**
     * 分片大小（字节）
     */
    private Long chunkSize;

    /**
     * 总分片数
     */
    private Integer totalChunks;

    /**
     * 已上传分片数
     */
    private Integer uploadedChunks;

    /**
     * 文件MD5值
     */
    private String fileMd5;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 上传状态：0-初始化，1-上传中，2-暂停，3-完成，4-失败
     */
    private Integer status;

    /**
     * 上传进度百分比
     */
    private Double progress;

    /**
     * 所属林班ID
     */
    private Long linbanId;

    /**
     * 所属林班名称
     */
    private String linbanName;

    /**
     * 采集时间
     */
    private LocalDateTime collectTime;

    /**
     * 采集人员
     */
    private String collector;

    /**
     * 上传用户ID（可选，如果需要鉴权）
     */
    private Long userId;

    /**
     * 上传用户imToken（用于鉴权）
     */
    private String imToken;

    /**
     * 最终文件URL（上传完成后）
     */
    private String finalFileUrl;

    /**
     * 临时存储路径
     */
    private String tempPath;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveTime;

}

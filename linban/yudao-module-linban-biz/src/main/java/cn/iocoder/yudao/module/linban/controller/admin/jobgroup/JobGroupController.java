package cn.iocoder.yudao.module.linban.controller.admin.jobgroup;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.linban.controller.admin.jobgroup.vo.*;
import cn.iocoder.yudao.module.linban.dal.dataobject.jobgroup.JobGroupDO;
import cn.iocoder.yudao.module.linban.service.jobgroup.JobGroupService;

@Tag(name = "管理后台 - 林班-工作分类")
@RestController
@RequestMapping("/linban/job-group")
@Validated
public class JobGroupController {

    @Resource
    private JobGroupService jobGroupService;

    @PostMapping("/create")
    @Operation(summary = "创建林班-工作分类")
    @PreAuthorize("@ss.hasPermission('linban:job-group:create')")
    public CommonResult<Long> createJobGroup(@Valid @RequestBody JobGroupSaveReqVO createReqVO) {
        return success(jobGroupService.createJobGroup(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新林班-工作分类")
    @PreAuthorize("@ss.hasPermission('linban:job-group:update')")
    public CommonResult<Boolean> updateJobGroup(@Valid @RequestBody JobGroupSaveReqVO updateReqVO) {
        jobGroupService.updateJobGroup(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除林班-工作分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('linban:job-group:delete')")
    public CommonResult<Boolean> deleteJobGroup(@RequestParam("id") Long id) {
        jobGroupService.deleteJobGroup(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得林班-工作分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('linban:job-group:query')")
    public CommonResult<JobGroupRespVO> getJobGroup(@RequestParam("id") Long id) {
        JobGroupDO jobGroup = jobGroupService.getJobGroup(id);
        return success(BeanUtils.toBean(jobGroup, JobGroupRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得林班-工作分类分页")
    @PreAuthorize("@ss.hasPermission('linban:job-group:query')")
    public CommonResult<PageResult<JobGroupRespVO>> getJobGroupPage(@Valid JobGroupPageReqVO pageReqVO) {
        PageResult<JobGroupDO> pageResult = jobGroupService.getJobGroupPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, JobGroupRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出林班-工作分类 Excel")
    @PreAuthorize("@ss.hasPermission('linban:job-group:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportJobGroupExcel(@Valid JobGroupPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<JobGroupDO> list = jobGroupService.getJobGroupPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "林班-工作分类.xls", "数据", JobGroupRespVO.class,
                        BeanUtils.toBean(list, JobGroupRespVO.class));
    }

}
package cn.iocoder.yudao.module.linban.job;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.module.linban.service.upload.ChunkUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 分片上传清理定时任务
 * 用于清理过期的上传任务和临时文件
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ChunkUploadCleanupJob implements JobHandler {

    @Resource
    private ChunkUploadService chunkUploadService;

    @Override
    public String execute(String param) throws Exception {
        log.info("开始执行分片上传清理任务");
        
        try {
            chunkUploadService.cleanExpiredUploads();
            log.info("分片上传清理任务执行完成");
            return "清理任务执行成功";
        } catch (Exception e) {
            log.error("分片上传清理任务执行失败", e);
            throw e;
        }
    }
}

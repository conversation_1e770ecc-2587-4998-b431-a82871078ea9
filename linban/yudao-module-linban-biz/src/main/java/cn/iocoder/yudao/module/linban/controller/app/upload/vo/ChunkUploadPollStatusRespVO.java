package cn.iocoder.yudao.module.linban.controller.app.upload.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户App - 轮询状态 Response VO")
@Data
public class ChunkUploadPollStatusRespVO {

    @Schema(description = "是否需要继续轮询")
    private Boolean needPoll;

    @Schema(description = "未完成文件数量")
    private Integer incompleteCount;

    @Schema(description = "上传中文件数量")
    private Integer uploadingCount;

    @Schema(description = "暂停文件数量")
    private Integer pausedCount;

    @Schema(description = "建议轮询间隔（毫秒）", example = "5000")
    private Long suggestedInterval;

    @Schema(description = "状态描述")
    private String message;

} 
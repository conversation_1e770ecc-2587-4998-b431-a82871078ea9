package cn.iocoder.yudao.module.linban.service.upload;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.linban.controller.app.upload.vo.*;
import cn.iocoder.yudao.module.linban.dal.dataobject.upload.ChunkDetailDO;
import cn.iocoder.yudao.module.linban.dal.dataobject.upload.ChunkUploadDO;
import cn.iocoder.yudao.module.linban.dal.mysql.upload.ChunkDetailMapper;
import cn.iocoder.yudao.module.linban.dal.mysql.upload.ChunkUploadMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.linban.enums.ErrorCodeConstants.*;

/**
 * 分片上传 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ChunkUploadServiceImpl implements ChunkUploadService {

    @Resource
    private ChunkUploadMapper chunkUploadMapper;

    @Resource
    private ChunkDetailMapper chunkDetailMapper;

    @Resource
    private FileApi fileApi;

    // 临时文件存储路径
    private static final String TEMP_UPLOAD_PATH = System.getProperty("java.io.tmpdir") + "/linban-upload/";

    @Override
    @Transactional
    public ChunkUploadInitRespVO initChunkUpload(@Valid ChunkUploadInitReqVO createReqVO) {
        // 检查是否已存在相同文件的上传任务
        ChunkUploadDO existingUpload = findExistingUpload(createReqVO.getFileMd5(), createReqVO.getTotalSize());
        
        if (existingUpload != null && existingUpload.getStatus() != 3 && existingUpload.getStatus() != 4) {
            // 存在未完成的上传任务，返回断点续传信息
            return buildResumeResponse(existingUpload);
        }

        // 创建新的上传任务
        String uploadId = IdUtil.simpleUUID();
        int totalChunks = (int) Math.ceil((double) createReqVO.getTotalSize() / createReqVO.getChunkSize());

        ChunkUploadDO chunkUpload = ChunkUploadDO.builder()
                .uploadId(uploadId)
                .fileName(createReqVO.getFileName())
                .totalSize(createReqVO.getTotalSize())
                .chunkSize(createReqVO.getChunkSize())
                .totalChunks(totalChunks)
                .uploadedChunks(0)
                .fileMd5(createReqVO.getFileMd5())
                .fileType(createReqVO.getFileType())
                .status(0) // 初始化状态
                .progress(0.0)
                .linbanId(createReqVO.getLinbanId())
                .linbanName(createReqVO.getLinbanName())
                .collectTime(createReqVO.getCollectTime())
                .collector(createReqVO.getCollector())
                .imToken(createReqVO.getImToken())
                .tempPath(TEMP_UPLOAD_PATH + uploadId + "/")
                .lastActiveTime(LocalDateTime.now())
                .build();

        chunkUploadMapper.insert(chunkUpload);

        // 初始化分片详情记录
        initChunkDetails(uploadId, totalChunks);

        // 创建临时目录
        createTempDirectory(chunkUpload.getTempPath());

        ChunkUploadInitRespVO response = new ChunkUploadInitRespVO();
        response.setUploadId(uploadId);
        response.setTotalChunks(totalChunks);
        response.setUploadedChunks(new ArrayList<>());
        response.setIsResume(false);
        response.setProgress(0.0);

        return response;
    }

    @Override
    @Transactional
    public ChunkUploadRespVO uploadChunk(@Valid ChunkUploadReqVO uploadReqVO) {
        // 获取上传任务
        ChunkUploadDO chunkUpload = validateAndGetUploadTask(uploadReqVO.getUploadId());
        
        // 检查分片是否已上传
        ChunkDetailDO chunkDetail = chunkDetailMapper.selectByUploadIdAndChunkIndex(
                uploadReqVO.getUploadId(), uploadReqVO.getChunkIndex());
        
        if (chunkDetail != null && chunkDetail.getStatus() == 2) {
            // 分片已上传成功，直接返回
            return buildChunkUploadResponse(chunkUpload, uploadReqVO.getChunkIndex(), true);
        }

        try {
            // 验证分片MD5
            byte[] chunkData = IoUtil.readBytes(uploadReqVO.getChunkFile().getInputStream());
            String actualMd5 = DigestUtil.md5Hex(chunkData);
            if (!actualMd5.equals(uploadReqVO.getChunkMd5())) {
                throw exception(CHUNK_MD5_MISMATCH);
            }

            // 保存分片文件
            String chunkPath = saveChunkFile(chunkUpload.getTempPath(), uploadReqVO.getChunkIndex(), chunkData);

            // 更新分片详情
            updateChunkDetail(uploadReqVO.getUploadId(), uploadReqVO.getChunkIndex(), 
                    chunkData.length, uploadReqVO.getChunkMd5(), chunkPath);

            // 更新上传任务进度
            updateUploadProgress(chunkUpload);

            return buildChunkUploadResponse(chunkUpload, uploadReqVO.getChunkIndex(), true);

        } catch (IOException e) {
            log.error("上传分片失败", e);
            // 更新分片状态为失败
            updateChunkDetailStatus(uploadReqVO.getUploadId(), uploadReqVO.getChunkIndex(), 3, e.getMessage());
            throw exception(CHUNK_UPLOAD_FAILED);
        }
    }

    @Override
    @Transactional
    public ChunkUploadCompleteRespVO completeChunkUpload(@Valid ChunkUploadCompleteReqVO completeReqVO) {
        ChunkUploadDO chunkUpload = validateAndGetUploadTask(completeReqVO.getUploadId());

        // 检查所有分片是否都已上传
        int uploadedCount = chunkDetailMapper.countUploadedChunks(completeReqVO.getUploadId());
        if (uploadedCount != chunkUpload.getTotalChunks()) {
            throw exception(UPLOAD_NOT_COMPLETE);
        }

        try {
            // 合并分片文件
            String finalFilePath = mergeChunkFiles(chunkUpload);

            // 上传到文件存储系统
            String finalFileUrl = uploadToFileStorage(chunkUpload.getFileName(), finalFilePath);

            // 更新上传任务状态
            chunkUpload.setStatus(3); // 完成
            chunkUpload.setProgress(100.0);
            chunkUpload.setFinalFileUrl(finalFileUrl);
            chunkUpload.setLastActiveTime(LocalDateTime.now());
            chunkUploadMapper.updateById(chunkUpload);

            // 清理临时文件
            cleanupTempFiles(chunkUpload.getTempPath());

            ChunkUploadCompleteRespVO response = new ChunkUploadCompleteRespVO();
            response.setUploadId(chunkUpload.getUploadId());
            response.setFileName(chunkUpload.getFileName());
            response.setFinalFileUrl(finalFileUrl);
            response.setFileSize(chunkUpload.getTotalSize());
            response.setSuccess(true);

            return response;

        } catch (Exception e) {
            log.error("完成上传失败", e);
            chunkUpload.setStatus(4); // 失败
            chunkUpload.setErrorMessage(e.getMessage());
            chunkUploadMapper.updateById(chunkUpload);

            ChunkUploadCompleteRespVO response = new ChunkUploadCompleteRespVO();
            response.setUploadId(chunkUpload.getUploadId());
            response.setSuccess(false);
            response.setErrorMessage(e.getMessage());
            return response;
        }
    }

    @Override
    public void pauseUpload(String uploadId) {
        ChunkUploadDO chunkUpload = validateAndGetUploadTask(uploadId);
        chunkUpload.setStatus(2); // 暂停
        chunkUpload.setLastActiveTime(LocalDateTime.now());
        chunkUploadMapper.updateById(chunkUpload);
    }

    @Override
    public void resumeUpload(String uploadId) {
        ChunkUploadDO chunkUpload = validateAndGetUploadTask(uploadId);
        chunkUpload.setStatus(1); // 上传中
        chunkUpload.setLastActiveTime(LocalDateTime.now());
        chunkUploadMapper.updateById(chunkUpload);
    }

    @Override
    @Transactional
    public void cancelUpload(String uploadId) {
        ChunkUploadDO chunkUpload = validateAndGetUploadTask(uploadId);

        // 清理临时文件
        cleanupTempFiles(chunkUpload.getTempPath());

        // 删除数据库记录
        chunkUploadMapper.deleteById(chunkUpload.getId());
        chunkDetailMapper.delete(chunkDetailMapper.lambdaQuery()
                .eq(ChunkDetailDO::getUploadId, uploadId));
    }

    @Override
    public ChunkUploadProgressRespVO getUploadProgress(String uploadId) {
        ChunkUploadDO chunkUpload = chunkUploadMapper.selectByUploadId(uploadId);
        if (chunkUpload == null) {
            throw exception(UPLOAD_TASK_NOT_FOUND);
        }

        return BeanUtils.toBean(chunkUpload, ChunkUploadProgressRespVO.class);
    }

    @Override
    public PageResult<ChunkUploadDO> getChunkUploadPage(ChunkUploadPageReqVO pageReqVO) {
        return chunkUploadMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional
    public void deleteChunkUpload(Long id) {
        ChunkUploadDO chunkUpload = chunkUploadMapper.selectById(id);
        if (chunkUpload != null) {
            cleanupTempFiles(chunkUpload.getTempPath());
            chunkUploadMapper.deleteById(id);
            chunkDetailMapper.delete(chunkDetailMapper.lambdaQuery()
                    .eq(ChunkDetailDO::getUploadId, chunkUpload.getUploadId()));
        }
    }

    @Override
    public void cleanExpiredUploads() {
        // 清理7天前的过期上传任务
        LocalDateTime expireTime = LocalDateTime.now().minusDays(7);
        List<ChunkUploadDO> expiredUploads = chunkUploadMapper.selectList(
                chunkUploadMapper.lambdaQuery()
                        .lt(ChunkUploadDO::getLastActiveTime, expireTime)
                        .in(ChunkUploadDO::getStatus, 0, 1, 2, 4)); // 非完成状态

        for (ChunkUploadDO upload : expiredUploads) {
            deleteChunkUpload(upload.getId());
        }
    }

    // 私有辅助方法实现
    private ChunkUploadDO findExistingUpload(String fileMd5, Long totalSize) {
        return chunkUploadMapper.selectOne(chunkUploadMapper.lambdaQuery()
                .eq(ChunkUploadDO::getFileMd5, fileMd5)
                .eq(ChunkUploadDO::getTotalSize, totalSize)
                .in(ChunkUploadDO::getStatus, 0, 1, 2) // 非完成和失败状态
                .orderByDesc(ChunkUploadDO::getCreateTime)
                .last("LIMIT 1"));
    }

    private ChunkUploadInitRespVO buildResumeResponse(ChunkUploadDO existingUpload) {
        List<ChunkDetailDO> uploadedChunks = chunkDetailMapper.selectUploadedChunks(existingUpload.getUploadId());
        List<Integer> uploadedChunkIndexes = uploadedChunks.stream()
                .map(ChunkDetailDO::getChunkIndex)
                .collect(Collectors.toList());

        ChunkUploadInitRespVO response = new ChunkUploadInitRespVO();
        response.setUploadId(existingUpload.getUploadId());
        response.setTotalChunks(existingUpload.getTotalChunks());
        response.setUploadedChunks(uploadedChunkIndexes);
        response.setIsResume(true);
        response.setProgress(existingUpload.getProgress());

        return response;
    }

    private void initChunkDetails(String uploadId, int totalChunks) {
        List<ChunkDetailDO> chunkDetails = new ArrayList<>();
        for (int i = 0; i < totalChunks; i++) {
            ChunkDetailDO chunkDetail = ChunkDetailDO.builder()
                    .uploadId(uploadId)
                    .chunkIndex(i)
                    .status(0) // 未上传
                    .retryCount(0)
                    .build();
            chunkDetails.add(chunkDetail);
        }
        chunkDetailMapper.insertBatch(chunkDetails);
    }

    private void createTempDirectory(String tempPath) {
        try {
            Path path = Paths.get(tempPath);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
            }
        } catch (IOException e) {
            log.error("创建临时目录失败: {}", tempPath, e);
            throw exception(CREATE_TEMP_DIR_FAILED);
        }
    }

    private ChunkUploadDO validateAndGetUploadTask(String uploadId) {
        ChunkUploadDO chunkUpload = chunkUploadMapper.selectByUploadId(uploadId);
        if (chunkUpload == null) {
            throw exception(UPLOAD_TASK_NOT_FOUND);
        }
        return chunkUpload;
    }

    private String saveChunkFile(String tempPath, Integer chunkIndex, byte[] chunkData) throws IOException {
        String chunkFileName = String.format("chunk_%d", chunkIndex);
        String chunkFilePath = tempPath + chunkFileName;

        try (FileOutputStream fos = new FileOutputStream(chunkFilePath)) {
            fos.write(chunkData);
            fos.flush();
        }

        return chunkFilePath;
    }

    private void updateChunkDetail(String uploadId, Integer chunkIndex, long chunkSize, String chunkMd5, String chunkPath) {
        ChunkDetailDO chunkDetail = chunkDetailMapper.selectByUploadIdAndChunkIndex(uploadId, chunkIndex);
        if (chunkDetail == null) {
            chunkDetail = ChunkDetailDO.builder()
                    .uploadId(uploadId)
                    .chunkIndex(chunkIndex)
                    .build();
        }

        chunkDetail.setChunkSize(chunkSize);
        chunkDetail.setChunkMd5(chunkMd5);
        chunkDetail.setChunkPath(chunkPath);
        chunkDetail.setStatus(2); // 上传成功
        chunkDetail.setErrorMessage(null);

        if (chunkDetail.getId() == null) {
            chunkDetailMapper.insert(chunkDetail);
        } else {
            chunkDetailMapper.updateById(chunkDetail);
        }
    }

    private void updateUploadProgress(ChunkUploadDO chunkUpload) {
        int uploadedCount = chunkDetailMapper.countUploadedChunks(chunkUpload.getUploadId());
        double progress = (double) uploadedCount / chunkUpload.getTotalChunks() * 100;

        chunkUpload.setUploadedChunks(uploadedCount);
        chunkUpload.setProgress(progress);
        chunkUpload.setStatus(1); // 上传中
        chunkUpload.setLastActiveTime(LocalDateTime.now());

        chunkUploadMapper.updateById(chunkUpload);
    }

    private ChunkUploadRespVO buildChunkUploadResponse(ChunkUploadDO chunkUpload, Integer chunkIndex, boolean success) {
        // 重新获取最新的上传进度
        ChunkUploadDO latestUpload = chunkUploadMapper.selectById(chunkUpload.getId());

        ChunkUploadRespVO response = new ChunkUploadRespVO();
        response.setUploadId(latestUpload.getUploadId());
        response.setChunkIndex(chunkIndex);
        response.setSuccess(success);
        response.setProgress(latestUpload.getProgress());
        response.setUploadedChunks(latestUpload.getUploadedChunks());
        response.setTotalChunks(latestUpload.getTotalChunks());

        return response;
    }

    private void updateChunkDetailStatus(String uploadId, Integer chunkIndex, int status, String errorMessage) {
        ChunkDetailDO chunkDetail = chunkDetailMapper.selectByUploadIdAndChunkIndex(uploadId, chunkIndex);
        if (chunkDetail != null) {
            chunkDetail.setStatus(status);
            chunkDetail.setErrorMessage(errorMessage);
            if (status == 3) { // 失败时增加重试次数
                chunkDetail.setRetryCount(chunkDetail.getRetryCount() + 1);
            }
            chunkDetailMapper.updateById(chunkDetail);
        }
    }

    private String mergeChunkFiles(ChunkUploadDO chunkUpload) throws IOException {
        String mergedFilePath = chunkUpload.getTempPath() + "merged_" + chunkUpload.getFileName();

        try (FileOutputStream fos = new FileOutputStream(mergedFilePath)) {
            for (int i = 0; i < chunkUpload.getTotalChunks(); i++) {
                String chunkFilePath = chunkUpload.getTempPath() + String.format("chunk_%d", i);
                try (FileInputStream fis = new FileInputStream(chunkFilePath)) {
                    IoUtil.copy(fis, fos);
                }
            }
        }

        // 验证合并后文件的MD5
        byte[] mergedFileData = Files.readAllBytes(Paths.get(mergedFilePath));
        String actualMd5 = DigestUtil.md5Hex(mergedFileData);
        if (!actualMd5.equals(chunkUpload.getFileMd5())) {
            throw new RuntimeException("合并后文件MD5校验失败");
        }

        return mergedFilePath;
    }

    private String uploadToFileStorage(String fileName, String filePath) {
        try {
            byte[] fileContent = Files.readAllBytes(Paths.get(filePath));
            return fileApi.createFile(fileName, null, fileContent);
        } catch (IOException e) {
            log.error("上传文件到存储系统失败", e);
            throw new RuntimeException("上传文件到存储系统失败", e);
        }
    }

    private void cleanupTempFiles(String tempPath) {
        try {
            Path path = Paths.get(tempPath);
            if (Files.exists(path)) {
                Files.walk(path)
                        .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                        .forEach(p -> {
                            try {
                                Files.deleteIfExists(p);
                            } catch (IOException e) {
                                log.warn("删除临时文件失败: {}", p, e);
                            }
                        });
            }
        } catch (IOException e) {
            log.error("清理临时文件失败: {}", tempPath, e);
        }
    }
}
}

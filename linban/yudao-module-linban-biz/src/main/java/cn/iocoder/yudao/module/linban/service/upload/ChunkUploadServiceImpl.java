package cn.iocoder.yudao.module.linban.service.upload;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.linban.controller.app.upload.vo.*;
import cn.iocoder.yudao.module.linban.dal.dataobject.upload.ChunkDetailDO;
import cn.iocoder.yudao.module.linban.dal.dataobject.upload.ChunkUploadDO;
import cn.iocoder.yudao.module.linban.dal.mysql.upload.ChunkDetailMapper;
import cn.iocoder.yudao.module.linban.dal.mysql.upload.ChunkUploadMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.linban.enums.ErrorCodeConstants.*;

/**
 * 分片上传 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ChunkUploadServiceImpl implements ChunkUploadService {

    @Resource
    private ChunkUploadMapper chunkUploadMapper;

    @Resource
    private ChunkDetailMapper chunkDetailMapper;

    @Resource
    private FileApi fileApi;

    // 临时文件存储路径
    private static final String TEMP_UPLOAD_PATH = System.getProperty("java.io.tmpdir") + "/linban-upload/";

    @Override
    @Transactional
    public ChunkUploadInitRespVO initChunkUpload(@Valid ChunkUploadInitReqVO createReqVO) {
        // 检查是否已存在相同文件的上传任务
        ChunkUploadDO existingUpload = findExistingUpload(createReqVO.getFileMd5(), createReqVO.getTotalSize());
        
        if (existingUpload != null && existingUpload.getStatus() != 3 && existingUpload.getStatus() != 4) {
            // 存在未完成的上传任务，返回断点续传信息
            return buildResumeResponse(existingUpload);
        }

        // 创建新的上传任务
        String uploadId = IdUtil.simpleUUID();
        int totalChunks = (int) Math.ceil((double) createReqVO.getTotalSize() / createReqVO.getChunkSize());

        ChunkUploadDO chunkUpload = ChunkUploadDO.builder()
                .uploadId(uploadId)
                .fileName(createReqVO.getFileName())
                .totalSize(createReqVO.getTotalSize())
                .chunkSize(createReqVO.getChunkSize())
                .totalChunks(totalChunks)
                .uploadedChunks(0)
                .fileMd5(createReqVO.getFileMd5())
                .fileType(createReqVO.getFileType())
                .status(0) // 初始化状态
                .progress(0.0)
                .linbanId(createReqVO.getLinbanId())
                .linbanName(createReqVO.getLinbanName())
                .collectTime(createReqVO.getCollectTime())
                .collector(createReqVO.getCollector())
                .imToken(createReqVO.getImToken())
                .tempPath(TEMP_UPLOAD_PATH + uploadId + "/")
                .lastActiveTime(LocalDateTime.now())
                .build();

        chunkUploadMapper.insert(chunkUpload);

        // 初始化分片详情记录
        initChunkDetails(uploadId, totalChunks);

        // 创建临时目录
        createTempDirectory(chunkUpload.getTempPath());

        ChunkUploadInitRespVO response = new ChunkUploadInitRespVO();
        response.setUploadId(uploadId);
        response.setTotalChunks(totalChunks);
        response.setUploadedChunks(new ArrayList<>());
        response.setIsResume(false);
        response.setProgress(0.0);

        return response;
    }

    @Override
    @Transactional
    public ChunkUploadRespVO uploadChunk(@Valid ChunkUploadReqVO uploadReqVO) {
        // 获取上传任务
        ChunkUploadDO chunkUpload = validateAndGetUploadTask(uploadReqVO.getUploadId());
        
        // 检查分片是否已上传
        ChunkDetailDO chunkDetail = chunkDetailMapper.selectByUploadIdAndChunkIndex(
                uploadReqVO.getUploadId(), uploadReqVO.getChunkIndex());
        
        if (chunkDetail != null && chunkDetail.getStatus() == 2) {
            // 分片已上传成功，直接返回
            return buildChunkUploadResponse(chunkUpload, uploadReqVO.getChunkIndex(), true);
        }

        try {
            // 验证分片MD5
            byte[] chunkData = IoUtil.readBytes(uploadReqVO.getChunkFile().getInputStream());
            String actualMd5 = DigestUtil.md5Hex(chunkData);
            if (!actualMd5.equals(uploadReqVO.getChunkMd5())) {
                throw exception(CHUNK_MD5_MISMATCH);
            }

            // 保存分片文件
            String chunkPath = saveChunkFile(chunkUpload.getTempPath(), uploadReqVO.getChunkIndex(), chunkData);

            // 更新分片详情
            updateChunkDetail(uploadReqVO.getUploadId(), uploadReqVO.getChunkIndex(), 
                    chunkData.length, uploadReqVO.getChunkMd5(), chunkPath);

            // 更新上传任务进度
            updateUploadProgress(chunkUpload);

            return buildChunkUploadResponse(chunkUpload, uploadReqVO.getChunkIndex(), true);

        } catch (IOException e) {
            log.error("上传分片失败", e);
            // 更新分片状态为失败
            updateChunkDetailStatus(uploadReqVO.getUploadId(), uploadReqVO.getChunkIndex(), 3, e.getMessage());
            throw exception(CHUNK_UPLOAD_FAILED);
        }
    }

    @Override
    @Transactional
    public ChunkUploadCompleteRespVO completeChunkUpload(@Valid ChunkUploadCompleteReqVO completeReqVO) {
        ChunkUploadDO chunkUpload = validateAndGetUploadTask(completeReqVO.getUploadId());

        // 检查所有分片是否都已上传
        Long uploadedCount = chunkDetailMapper.countUploadedChunks(completeReqVO.getUploadId());
        if (uploadedCount.intValue() != chunkUpload.getTotalChunks()) {
            throw exception(UPLOAD_NOT_COMPLETE);
        }

        try {
            // 合并分片文件
            String finalFilePath = mergeChunkFiles(chunkUpload);

            // 上传到文件存储系统
            String finalFileUrl = uploadToFileStorage(chunkUpload.getFileName(), finalFilePath);

            // 更新上传任务状态
            chunkUpload.setStatus(3); // 完成
            chunkUpload.setProgress(100.0);
            chunkUpload.setFinalFileUrl(finalFileUrl);
            chunkUpload.setLastActiveTime(LocalDateTime.now());
            chunkUploadMapper.updateById(chunkUpload);

            // 清理临时文件
            cleanupTempFiles(chunkUpload.getTempPath());

            ChunkUploadCompleteRespVO response = new ChunkUploadCompleteRespVO();
            response.setUploadId(chunkUpload.getUploadId());
            response.setFileName(chunkUpload.getFileName());
            response.setFinalFileUrl(finalFileUrl);
            response.setFileSize(chunkUpload.getTotalSize());
            response.setSuccess(true);

            return response;

        } catch (Exception e) {
            log.error("完成上传失败", e);
            chunkUpload.setStatus(4); // 失败
            chunkUpload.setErrorMessage(e.getMessage());
            chunkUploadMapper.updateById(chunkUpload);

            ChunkUploadCompleteRespVO response = new ChunkUploadCompleteRespVO();
            response.setUploadId(chunkUpload.getUploadId());
            response.setSuccess(false);
            response.setErrorMessage(e.getMessage());
            return response;
        }
    }

    @Override
    public void pauseUpload(String uploadId) {
        ChunkUploadDO chunkUpload = validateAndGetUploadTask(uploadId);
        chunkUpload.setStatus(2); // 暂停
        chunkUpload.setLastActiveTime(LocalDateTime.now());
        chunkUploadMapper.updateById(chunkUpload);
    }

    @Override
    public void resumeUpload(String uploadId) {
        ChunkUploadDO chunkUpload = validateAndGetUploadTask(uploadId);
        chunkUpload.setStatus(1); // 上传中
        chunkUpload.setLastActiveTime(LocalDateTime.now());
        chunkUploadMapper.updateById(chunkUpload);
    }

    @Override
    @Transactional
    public void cancelUpload(String uploadId) {
        ChunkUploadDO chunkUpload = validateAndGetUploadTask(uploadId);

        // 清理临时文件
        cleanupTempFiles(chunkUpload.getTempPath());

        // 删除数据库记录
        chunkUploadMapper.deleteById(chunkUpload.getId());
        chunkDetailMapper.delete(new LambdaQueryWrapperX<ChunkDetailDO>()
                .eq(ChunkDetailDO::getUploadId, uploadId));
    }

    @Override
    public ChunkUploadProgressRespVO getUploadProgress(String uploadId) {
        ChunkUploadDO chunkUpload = chunkUploadMapper.selectByUploadId(uploadId);
        if (chunkUpload == null) {
            throw exception(UPLOAD_TASK_NOT_FOUND);
        }

        ChunkUploadProgressRespVO respVO = BeanUtils.toBean(chunkUpload, ChunkUploadProgressRespVO.class);
        
        // 设置轮询控制信息
        setPollingControlInfo(respVO, chunkUpload);
        
        return respVO;
    }

    @Override
    public PageResult<ChunkUploadDO> getChunkUploadPage(ChunkUploadPageReqVO pageReqVO) {
        return chunkUploadMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional
    public void deleteChunkUpload(Long id) {
        ChunkUploadDO chunkUpload = chunkUploadMapper.selectById(id);
        if (chunkUpload != null) {
            cleanupTempFiles(chunkUpload.getTempPath());
            chunkUploadMapper.deleteById(id);
            chunkDetailMapper.delete(new LambdaQueryWrapperX<ChunkDetailDO>()
                    .eq(ChunkDetailDO::getUploadId, chunkUpload.getUploadId()));
        }
    }

    @Override
    public void cleanExpiredUploads() {
        // 清理7天前的过期上传任务
        LocalDateTime expireTime = LocalDateTime.now().minusDays(7);
        List<ChunkUploadDO> expiredUploads = chunkUploadMapper.selectList(
                new LambdaQueryWrapperX<ChunkUploadDO>()
                        .lt(ChunkUploadDO::getLastActiveTime, expireTime)
                        .in(ChunkUploadDO::getStatus, 0, 1, 2, 4)); // 非完成状态

        for (ChunkUploadDO upload : expiredUploads) {
            deleteChunkUpload(upload.getId());
        }
    }

    @Override
    public PageResult<ChunkUploadFileListRespVO> getFileList(ChunkUploadFileListReqVO pageReqVO) {
        // 查询文件列表
        PageResult<ChunkUploadDO> pageResult = chunkUploadMapper.selectFileListPage(pageReqVO);
        
        // 转换为响应VO
        List<ChunkUploadFileListRespVO> list = pageResult.getList().stream()
                .map(this::convertToFileListRespVO)
                .collect(Collectors.toList());
        
        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    public boolean hasIncompleteFiles() {
        // 轻量级查询：只检查是否存在未完成的上传任务
        Long count = chunkUploadMapper.selectCount(new LambdaQueryWrapperX<ChunkUploadDO>()
                .in(ChunkUploadDO::getStatus, 0, 1, 2)); // 初始化、上传中、暂停状态
        return count > 0;
    }

    @Override
    public ChunkUploadPollStatusRespVO getPollStatus() {
        ChunkUploadPollStatusRespVO respVO = new ChunkUploadPollStatusRespVO();
        
        // 查询各状态的文件数量
        Long uploadingCount = chunkUploadMapper.selectCount(new LambdaQueryWrapperX<ChunkUploadDO>()
                .eq(ChunkUploadDO::getStatus, 1)); // 上传中
        
        Long pausedCount = chunkUploadMapper.selectCount(new LambdaQueryWrapperX<ChunkUploadDO>()
                .eq(ChunkUploadDO::getStatus, 2)); // 暂停
        
        Long initializingCount = chunkUploadMapper.selectCount(new LambdaQueryWrapperX<ChunkUploadDO>()
                .eq(ChunkUploadDO::getStatus, 0)); // 初始化
        
        int totalIncomplete = uploadingCount.intValue() + pausedCount.intValue() + initializingCount.intValue();
        
        respVO.setIncompleteCount(totalIncomplete);
        respVO.setUploadingCount(uploadingCount.intValue());
        respVO.setPausedCount(pausedCount.intValue());
        respVO.setNeedPoll(totalIncomplete > 0);
        
        // 根据状态给出轮询建议
        if (totalIncomplete == 0) {
            respVO.setSuggestedInterval(0L); // 停止轮询
            respVO.setMessage("所有文件已完成，无需轮询");
        } else if (uploadingCount > 0) {
            respVO.setSuggestedInterval(3000L); // 3秒轮询
            respVO.setMessage(String.format("有 %d 个文件正在上传，建议3秒轮询一次", uploadingCount));
        } else {
            respVO.setSuggestedInterval(10000L); // 10秒轮询
            respVO.setMessage(String.format("有 %d 个文件暂停或待上传，建议10秒轮询一次", totalIncomplete));
        }
        
        return respVO;
    }

    // 私有辅助方法实现
    private ChunkUploadDO findExistingUpload(String fileMd5, Long totalSize) {
        return chunkUploadMapper.selectOne(new LambdaQueryWrapperX<ChunkUploadDO>()
                .eq(ChunkUploadDO::getFileMd5, fileMd5)
                .eq(ChunkUploadDO::getTotalSize, totalSize)
                .in(ChunkUploadDO::getStatus, 0, 1, 2) // 非完成和失败状态
                .orderByDesc(ChunkUploadDO::getCreateTime)
                .last("LIMIT 1"));
    }

    private ChunkUploadInitRespVO buildResumeResponse(ChunkUploadDO existingUpload) {
        List<ChunkDetailDO> uploadedChunks = chunkDetailMapper.selectUploadedChunks(existingUpload.getUploadId());
        List<Integer> uploadedChunkIndexes = uploadedChunks.stream()
                .map(ChunkDetailDO::getChunkIndex)
                .collect(Collectors.toList());

        ChunkUploadInitRespVO response = new ChunkUploadInitRespVO();
        response.setUploadId(existingUpload.getUploadId());
        response.setTotalChunks(existingUpload.getTotalChunks());
        response.setUploadedChunks(uploadedChunkIndexes);
        response.setIsResume(true);
        response.setProgress(existingUpload.getProgress());

        return response;
    }

    private void initChunkDetails(String uploadId, int totalChunks) {
        List<ChunkDetailDO> chunkDetails = new ArrayList<>();
        for (int i = 0; i < totalChunks; i++) {
            ChunkDetailDO chunkDetail = ChunkDetailDO.builder()
                    .uploadId(uploadId)
                    .chunkIndex(i)
                    .status(0) // 未上传
                    .retryCount(0)
                    .build();
            chunkDetails.add(chunkDetail);
        }
        chunkDetailMapper.insertBatch(chunkDetails);
    }

    private void createTempDirectory(String tempPath) {
        try {
            Path path = Paths.get(tempPath);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
            }
        } catch (IOException e) {
            log.error("创建临时目录失败: {}", tempPath, e);
            throw exception(CREATE_TEMP_DIR_FAILED);
        }
    }

    private ChunkUploadDO validateAndGetUploadTask(String uploadId) {
        ChunkUploadDO chunkUpload = chunkUploadMapper.selectByUploadId(uploadId);
        if (chunkUpload == null) {
            throw exception(UPLOAD_TASK_NOT_FOUND);
        }
        return chunkUpload;
    }

    private String saveChunkFile(String tempPath, Integer chunkIndex, byte[] chunkData) throws IOException {
        String chunkFileName = String.format("chunk_%d", chunkIndex);
        String chunkFilePath = tempPath + chunkFileName;

        try (FileOutputStream fos = new FileOutputStream(chunkFilePath)) {
            fos.write(chunkData);
            fos.flush();
        }

        return chunkFilePath;
    }

    private void updateChunkDetail(String uploadId, Integer chunkIndex, long chunkSize, String chunkMd5, String chunkPath) {
        ChunkDetailDO chunkDetail = chunkDetailMapper.selectByUploadIdAndChunkIndex(uploadId, chunkIndex);
        if (chunkDetail == null) {
            chunkDetail = ChunkDetailDO.builder()
                    .uploadId(uploadId)
                    .chunkIndex(chunkIndex)
                    .build();
        }

        chunkDetail.setChunkSize(chunkSize);
        chunkDetail.setChunkMd5(chunkMd5);
        chunkDetail.setChunkPath(chunkPath);
        chunkDetail.setStatus(2); // 上传成功
        chunkDetail.setErrorMessage(null);

        if (chunkDetail.getId() == null) {
            chunkDetailMapper.insert(chunkDetail);
        } else {
            chunkDetailMapper.updateById(chunkDetail);
        }
    }

    private void updateUploadProgress(ChunkUploadDO chunkUpload) {
        Long uploadedCount = chunkDetailMapper.countUploadedChunks(chunkUpload.getUploadId());
        double progress = (double) uploadedCount / chunkUpload.getTotalChunks() * 100;

        chunkUpload.setUploadedChunks(uploadedCount.intValue());
        chunkUpload.setProgress(progress);
        chunkUpload.setStatus(1); // 上传中
        chunkUpload.setLastActiveTime(LocalDateTime.now());

        chunkUploadMapper.updateById(chunkUpload);
    }

    private ChunkUploadRespVO buildChunkUploadResponse(ChunkUploadDO chunkUpload, Integer chunkIndex, boolean success) {
        // 重新获取最新的上传进度
        ChunkUploadDO latestUpload = chunkUploadMapper.selectById(chunkUpload.getId());

        ChunkUploadRespVO response = new ChunkUploadRespVO();
        response.setUploadId(latestUpload.getUploadId());
        response.setChunkIndex(chunkIndex);
        response.setSuccess(success);
        response.setProgress(latestUpload.getProgress());
        response.setUploadedChunks(latestUpload.getUploadedChunks());
        response.setTotalChunks(latestUpload.getTotalChunks());

        return response;
    }

    private void updateChunkDetailStatus(String uploadId, Integer chunkIndex, int status, String errorMessage) {
        ChunkDetailDO chunkDetail = chunkDetailMapper.selectByUploadIdAndChunkIndex(uploadId, chunkIndex);
        if (chunkDetail != null) {
            chunkDetail.setStatus(status);
            chunkDetail.setErrorMessage(errorMessage);
            if (status == 3) { // 失败时增加重试次数
                chunkDetail.setRetryCount(chunkDetail.getRetryCount() + 1);
            }
            chunkDetailMapper.updateById(chunkDetail);
        }
    }

    private String mergeChunkFiles(ChunkUploadDO chunkUpload) throws IOException {
        String mergedFilePath = chunkUpload.getTempPath() + "merged_" + chunkUpload.getFileName();
        
        log.info("开始合并文件分片: uploadId={}, totalChunks={}, filePath={}", 
                chunkUpload.getUploadId(), chunkUpload.getTotalChunks(), mergedFilePath);

        // 使用缓冲区优化大文件合并
        int bufferSize = 8192; // 8KB缓冲区
        byte[] buffer = new byte[bufferSize];
        
        try (FileOutputStream fos = new FileOutputStream(mergedFilePath);
             BufferedOutputStream bos = new BufferedOutputStream(fos, bufferSize * 4)) {
            
            for (int i = 0; i < chunkUpload.getTotalChunks(); i++) {
                String chunkFilePath = chunkUpload.getTempPath() + String.format("chunk_%d", i);
                File chunkFile = new File(chunkFilePath);
                
                // 检查分片文件是否存在
                if (!chunkFile.exists()) {
                    throw new IOException("分片文件不存在: " + chunkFilePath);
                }
                
                log.debug("合并分片 {}/{}: {}", i + 1, chunkUpload.getTotalChunks(), chunkFilePath);
                
                // 使用缓冲流复制文件，避免大文件内存问题
                try (FileInputStream fis = new FileInputStream(chunkFile);
                     BufferedInputStream bis = new BufferedInputStream(fis, bufferSize)) {
                    
                    int bytesRead;
                    while ((bytesRead = bis.read(buffer)) != -1) {
                        bos.write(buffer, 0, bytesRead);
                    }
                    bos.flush(); // 确保数据写入
                } catch (IOException e) {
                    log.error("合并分片文件失败: chunkIndex={}, chunkFile={}", i, chunkFilePath, e);
                    throw new IOException("合并分片文件失败: chunk_" + i, e);
                }
            }
            
            // 强制刷新缓冲区
            bos.flush();
            fos.getFD().sync(); // 确保数据同步到磁盘
        }

        log.info("文件分片合并完成，开始MD5校验: {}", mergedFilePath);

        // 使用流式MD5计算，避免大文件内存溢出
        String actualMd5 = calculateStreamMD5(mergedFilePath);
        if (!actualMd5.equals(chunkUpload.getFileMd5())) {
            // MD5校验失败，删除合并的文件
            try {
                Files.deleteIfExists(Paths.get(mergedFilePath));
            } catch (IOException e) {
                log.warn("删除MD5校验失败的合并文件失败: {}", mergedFilePath, e);
            }
            throw new RuntimeException(String.format("合并后文件MD5校验失败: 期望=%s, 实际=%s", 
                    chunkUpload.getFileMd5(), actualMd5));
        }

        log.info("文件合并和校验成功: uploadId={}, md5={}", chunkUpload.getUploadId(), actualMd5);
        return mergedFilePath;
    }

    private String uploadToFileStorage(String fileName, String filePath) {
        try {
            File file = new File(filePath);
            long fileSize = file.length();
            
            log.info("开始上传文件到存储系统: fileName={}, filePath={}, size={}", 
                    fileName, filePath, formatFileSize(fileSize));
            
            // 对于大文件（>100MB），使用流式上传
            if (fileSize > 100 * 1024 * 1024) {
                return uploadLargeFileToStorage(fileName, filePath);
            } else {
                // 小文件直接读取到内存
                byte[] fileContent = Files.readAllBytes(Paths.get(filePath));
                return fileApi.createFile(fileName, null, fileContent);
            }
            
        } catch (IOException e) {
            log.error("上传文件到存储系统失败: fileName={}, filePath={}", fileName, filePath, e);
            throw new RuntimeException("上传文件到存储系统失败", e);
        }
    }

    /**
     * 大文件流式上传到存储系统
     */
    private String uploadLargeFileToStorage(String fileName, String filePath) throws IOException {
        // 注意：这里需要根据实际的fileApi接口来实现
        // 如果fileApi不支持流式上传，可能需要分块上传或其他策略
        
        // 临时方案：分块读取到内存，但控制块大小
        int chunkSize = 50 * 1024 * 1024; // 50MB分块
        try (FileInputStream fis = new FileInputStream(filePath);
             BufferedInputStream bis = new BufferedInputStream(fis, 8192)) {
            
            // 先尝试直接读取（如果文件不是特别大）
            File file = new File(filePath);
            if (file.length() <= 500 * 1024 * 1024) { // 500MB以下还是直接读取
                byte[] fileContent = Files.readAllBytes(Paths.get(filePath));
                return fileApi.createFile(fileName, null, fileContent);
            }
            
            // 对于超大文件，需要特殊处理
            // TODO: 这里需要根据实际存储系统API来实现大文件上传策略
            log.warn("检测到超大文件({})，当前实现可能导致内存不足", formatFileSize(file.length()));
            
            // 临时仍使用直接读取，但添加警告
            byte[] fileContent = Files.readAllBytes(Paths.get(filePath));
            return fileApi.createFile(fileName, null, fileContent);
        }
    }

    private void cleanupTempFiles(String tempPath) {
        try {
            Path path = Paths.get(tempPath);
            if (Files.exists(path)) {
                Files.walk(path)
                        .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                        .forEach(p -> {
                            try {
                                Files.deleteIfExists(p);
                            } catch (IOException e) {
                                log.warn("删除临时文件失败: {}", p, e);
                            }
                        });
            }
        } catch (IOException e) {
            log.error("清理临时文件失败: {}", tempPath, e);
        }
    }

    /**
     * 转换ChunkUploadDO为ChunkUploadFileListRespVO
     */
    private ChunkUploadFileListRespVO convertToFileListRespVO(ChunkUploadDO uploadDO) {
        ChunkUploadFileListRespVO respVO = new ChunkUploadFileListRespVO();
        respVO.setUploadId(uploadDO.getUploadId());
        respVO.setFileName(uploadDO.getFileName());
        respVO.setFileType(uploadDO.getFileType());
        respVO.setTotalSize(uploadDO.getTotalSize());
        respVO.setFileSizeDisplay(formatFileSize(uploadDO.getTotalSize()));
        respVO.setFileMd5(uploadDO.getFileMd5());
        respVO.setFinalFileUrl(uploadDO.getFinalFileUrl());
        respVO.setLinbanId(uploadDO.getLinbanId());
        respVO.setLinbanName(uploadDO.getLinbanName());
        respVO.setCollector(uploadDO.getCollector());
        respVO.setCollectTime(uploadDO.getCollectTime());
        respVO.setCreateTime(uploadDO.getCreateTime());
        respVO.setTotalChunks(uploadDO.getTotalChunks());
        respVO.setPreviewUrl(generatePreviewUrl(uploadDO.getFinalFileUrl(), uploadDO.getFileType()));
        respVO.setSupportPreview(isSupportPreview(uploadDO.getFileType()));
        respVO.setLastActiveTime(uploadDO.getLastActiveTime());
        
        // 设置状态信息
        respVO.setStatus(uploadDO.getStatus());
        respVO.setStatusDesc(getStatusDescription(uploadDO.getStatus()));
        respVO.setErrorMessage(uploadDO.getErrorMessage());
        
        // 设置进度信息
        if (uploadDO.getStatus() == 3) {
            // 已完成
            respVO.setProgress(100.0);
            respVO.setUploadedChunks(uploadDO.getTotalChunks());
            respVO.setCompleteTime(uploadDO.getLastActiveTime());
            
            // 计算上传耗时
            if (uploadDO.getCreateTime() != null && uploadDO.getLastActiveTime() != null) {
                long duration = Duration.between(uploadDO.getCreateTime(), uploadDO.getLastActiveTime()).getSeconds();
                respVO.setUploadDuration(duration);
            }
        } else {
            // 未完成状态，获取实时进度
            setRealTimeProgress(respVO, uploadDO);
        }
        
        return respVO;
    }

    /**
     * 格式化文件大小显示
     */
    private String formatFileSize(Long sizeInBytes) {
        if (sizeInBytes == null || sizeInBytes <= 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = sizeInBytes.doubleValue();
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }

    /**
     * 生成预览URL
     */
    private String generatePreviewUrl(String fileUrl, String fileType) {
        if (fileUrl == null || !isSupportPreview(fileType)) {
            return null;
        }
        // 这里可以根据实际需求生成预览URL
        // 例如：对于图片直接返回原URL，对于视频生成缩略图等
        return fileUrl;
    }

    /**
     * 判断文件类型是否支持在线预览
     */
    private Boolean isSupportPreview(String fileType) {
        if (fileType == null) {
            return false;
        }
        
        String lowerCaseType = fileType.toLowerCase();
        // 支持预览的文件类型
        return lowerCaseType.startsWith("image/") ||
               lowerCaseType.startsWith("video/") ||
               lowerCaseType.equals("application/pdf") ||
               lowerCaseType.startsWith("text/");
    }

    /**
     * 获取状态描述
     */
    private String getStatusDescription(Integer status) {
        if (status == null) {
            return "未知状态";
        }
        switch (status) {
            case 0: return "初始化";
            case 1: return "上传中";
            case 2: return "已暂停";
            case 3: return "已完成";
            case 4: return "上传失败";
            default: return "未知状态";
        }
    }

    /**
     * 设置实时进度信息（针对未完成的文件）
     */
    private void setRealTimeProgress(ChunkUploadFileListRespVO respVO, ChunkUploadDO uploadDO) {
        try {
            // 获取实时上传进度
            Long uploadedCount = chunkDetailMapper.countUploadedChunks(uploadDO.getUploadId());
            double progress = uploadDO.getTotalChunks() > 0 ? 
                (double) uploadedCount / uploadDO.getTotalChunks() * 100 : 0.0;
            
            respVO.setProgress(Math.round(progress * 100.0) / 100.0); // 保留两位小数
            respVO.setUploadedChunks(uploadedCount.intValue());
            
            // 计算上传速度和预计剩余时间（仅对上传中的任务）
            if (uploadDO.getStatus() == 1 && uploadDO.getCreateTime() != null) {
                long elapsedSeconds = Duration.between(uploadDO.getCreateTime(), LocalDateTime.now()).getSeconds();
                if (elapsedSeconds > 0 && uploadedCount > 0) {
                    // 计算平均上传速度（字节/秒）
                    long uploadedBytes = uploadedCount * uploadDO.getChunkSize();
                    long speed = uploadedBytes / elapsedSeconds;
                    respVO.setUploadSpeed(speed);
                    
                    // 计算预计剩余时间
                    long remainingBytes = (uploadDO.getTotalChunks() - uploadedCount) * uploadDO.getChunkSize();
                    if (speed > 0) {
                        long estimatedTime = remainingBytes / speed;
                        respVO.setEstimatedRemainingTime(estimatedTime);
                    }
                }
            }
            
        } catch (Exception e) {
            log.warn("获取实时进度失败: uploadId={}", uploadDO.getUploadId(), e);
            // 使用数据库中的进度作为兜底
            respVO.setProgress(uploadDO.getProgress() != null ? uploadDO.getProgress() : 0.0);
            respVO.setUploadedChunks(uploadDO.getUploadedChunks() != null ? uploadDO.getUploadedChunks() : 0);
        }
    }

    /**
     * 流式计算文件MD5，避免大文件内存溢出
     */
    private String calculateStreamMD5(String filePath) throws IOException {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            int bufferSize = 8192; // 8KB缓冲区
            byte[] buffer = new byte[bufferSize];
            
            try (FileInputStream fis = new FileInputStream(filePath);
                 BufferedInputStream bis = new BufferedInputStream(fis, bufferSize)) {
                
                int bytesRead;
                while ((bytesRead = bis.read(buffer)) != -1) {
                    md5.update(buffer, 0, bytesRead);
                }
            }
            
            // 转换为16进制字符串
            byte[] digest = md5.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
            
        } catch (NoSuchAlgorithmException e) {
            throw new IOException("MD5算法不可用", e);
        }
    }

    /**
     * 设置轮询控制信息
     */
    private void setPollingControlInfo(ChunkUploadProgressRespVO respVO, ChunkUploadDO chunkUpload) {
        Integer status = chunkUpload.getStatus();
        
        if (status == null) {
            respVO.setNeedContinuePolling(false);
            respVO.setSuggestedPollingInterval(0L);
            respVO.setPollingMessage("状态未知，停止轮询");
            return;
        }
        
        switch (status) {
            case 0: // 初始化
                respVO.setNeedContinuePolling(true);
                respVO.setSuggestedPollingInterval(5000L); // 5秒
                respVO.setPollingMessage("文件初始化中，建议5秒后查询");
                break;
                
            case 1: // 上传中
                respVO.setNeedContinuePolling(true);
                respVO.setSuggestedPollingInterval(2000L); // 2秒
                respVO.setPollingMessage(String.format("文件上传中(%.1f%%)，建议2秒轮询", 
                        chunkUpload.getProgress() != null ? chunkUpload.getProgress() : 0.0));
                break;
                
            case 2: // 暂停
                respVO.setNeedContinuePolling(true);
                respVO.setSuggestedPollingInterval(10000L); // 10秒
                respVO.setPollingMessage("文件上传已暂停，建议10秒轮询检查是否恢复");
                break;
                
            case 3: // 已完成
                respVO.setNeedContinuePolling(false);
                respVO.setSuggestedPollingInterval(0L);
                respVO.setPollingMessage("文件上传已完成，无需继续轮询");
                break;
                
            case 4: // 失败
                respVO.setNeedContinuePolling(false);
                respVO.setSuggestedPollingInterval(0L);
                respVO.setPollingMessage("文件上传失败，无需继续轮询");
                break;
                
            default:
                respVO.setNeedContinuePolling(false);
                respVO.setSuggestedPollingInterval(0L);
                respVO.setPollingMessage("未知状态，停止轮询");
                break;
        }
        
        log.debug("设置轮询控制信息: uploadId={}, status={}, needPolling={}, interval={}ms", 
                chunkUpload.getUploadId(), status, respVO.getNeedContinuePolling(), 
                respVO.getSuggestedPollingInterval());
    }
}

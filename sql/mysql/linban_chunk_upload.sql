-- 分片上传记录表
CREATE TABLE `linban_chunk_upload` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `upload_id` varchar(64) NOT NULL COMMENT '上传任务ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `total_size` bigint NOT NULL COMMENT '文件总大小（字节）',
  `chunk_size` bigint NOT NULL COMMENT '分片大小（字节）',
  `total_chunks` int NOT NULL COMMENT '总分片数',
  `uploaded_chunks` int NOT NULL DEFAULT '0' COMMENT '已上传分片数',
  `file_md5` varchar(32) NOT NULL COMMENT '文件MD5值',
  `file_type` varchar(100) DEFAULT NULL COMMENT '文件类型',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '上传状态：0-初始化，1-上传中，2-暂停，3-完成，4-失败',
  `progress` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '上传进度百分比',
  `linban_id` bigint NOT NULL COMMENT '所属林班ID',
  `linban_name` varchar(100) NOT NULL COMMENT '所属林班名称',
  `collect_time` datetime NOT NULL COMMENT '采集时间',
  `collector` varchar(100) NOT NULL COMMENT '采集人员',
  `user_id` bigint DEFAULT NULL COMMENT '上传用户ID',
  `im_token` varchar(500) DEFAULT NULL COMMENT '用户imToken',
  `final_file_url` varchar(500) DEFAULT NULL COMMENT '最终文件URL',
  `temp_path` varchar(500) DEFAULT NULL COMMENT '临时存储路径',
  `error_message` text COMMENT '错误信息',
  `last_active_time` datetime NOT NULL COMMENT '最后活跃时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_upload_id` (`upload_id`),
  KEY `idx_file_md5_size` (`file_md5`, `total_size`),
  KEY `idx_status` (`status`),
  KEY `idx_linban_id` (`linban_id`),
  KEY `idx_last_active_time` (`last_active_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分片上传记录表';

-- 分片详情表
CREATE TABLE `linban_chunk_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `upload_id` varchar(64) NOT NULL COMMENT '上传任务ID',
  `chunk_index` int NOT NULL COMMENT '分片序号（从0开始）',
  `chunk_size` bigint DEFAULT NULL COMMENT '分片大小（字节）',
  `chunk_md5` varchar(32) DEFAULT NULL COMMENT '分片MD5值',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '分片状态：0-未上传，1-上传中，2-上传成功，3-上传失败',
  `chunk_path` varchar(500) DEFAULT NULL COMMENT '分片存储路径',
  `retry_count` int NOT NULL DEFAULT '0' COMMENT '重试次数',
  `error_message` text COMMENT '错误信息',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_upload_chunk` (`upload_id`, `chunk_index`),
  KEY `idx_upload_id` (`upload_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分片详情表';

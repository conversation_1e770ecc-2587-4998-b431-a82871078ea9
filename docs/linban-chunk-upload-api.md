# 林班大文件分片上传API文档

## 概述

林班系统扩展的大文件上传接口，支持分片上传、断点续传、暂停/恢复、进度查看等功能。上传文件时需要提供所属林班、采集时间、采集人员等元数据信息。

## 特性

- ✅ 大文件分片上传
- ✅ 断点续传
- ✅ 暂停/恢复上传
- ✅ 实时进度查看
- ✅ 文件完整性校验（MD5）
- ✅ 林班元数据支持
- ✅ 可选的imToken鉴权
- ✅ 自动清理过期任务

## API接口

### 1. 初始化分片上传

**接口地址：** `POST /app-api/linban/upload/init`

**请求参数：**
```json
{
  "fileName": "forest_video.mp4",
  "totalSize": 104857600,
  "chunkSize": 5242880,
  "fileMd5": "d41d8cd98f00b204e9800998ecf8427e",
  "fileType": "video/mp4",
  "linbanId": 1,
  "linbanName": "东山林班",
  "collectTime": "2024-01-15T10:30:00",
  "collector": "张三",
  "imToken": "optional_im_token"
}
```

**响应结果：**
```json
{
  "code": 0,
  "data": {
    "uploadId": "abc123def456",
    "totalChunks": 20,
    "uploadedChunks": [],
    "isResume": false,
    "progress": 0.0
  },
  "msg": "操作成功"
}
```

### 2. 上传分片

**接口地址：** `POST /app-api/linban/upload/chunk`

**请求参数：** (multipart/form-data)
- `uploadId`: 上传任务ID
- `chunkIndex`: 分片序号（从0开始）
- `chunkFile`: 分片文件
- `chunkMd5`: 分片MD5值

**响应结果：**
```json
{
  "code": 0,
  "data": {
    "uploadId": "abc123def456",
    "chunkIndex": 0,
    "success": true,
    "progress": 5.0,
    "uploadedChunks": 1,
    "totalChunks": 20
  },
  "msg": "操作成功"
}
```

### 3. 完成上传

**接口地址：** `POST /app-api/linban/upload/complete`

**请求参数：**
```json
{
  "uploadId": "abc123def456"
}
```

**响应结果：**
```json
{
  "code": 0,
  "data": {
    "uploadId": "abc123def456",
    "fileName": "forest_video.mp4",
    "finalFileUrl": "https://storage.example.com/files/forest_video.mp4",
    "fileSize": 104857600,
    "success": true
  },
  "msg": "操作成功"
}
```

### 4. 暂停上传

**接口地址：** `POST /app-api/linban/upload/pause?uploadId=abc123def456`

### 5. 恢复上传

**接口地址：** `POST /app-api/linban/upload/resume?uploadId=abc123def456`

### 6. 取消上传

**接口地址：** `DELETE /app-api/linban/upload/cancel?uploadId=abc123def456`

### 7. 查询上传进度

**接口地址：** `GET /app-api/linban/upload/progress?uploadId=abc123def456`

**响应结果：**
```json
{
  "code": 0,
  "data": {
    "uploadId": "abc123def456",
    "fileName": "forest_video.mp4",
    "totalSize": 104857600,
    "status": 1,
    "progress": 75.0,
    "uploadedChunks": 15,
    "totalChunks": 20,
    "linbanName": "东山林班",
    "collector": "张三",
    "collectTime": "2024-01-15T10:30:00",
    "lastActiveTime": "2024-01-15T11:00:00"
  },
  "msg": "操作成功"
}
```

## 状态码说明

- `0`: 初始化
- `1`: 上传中
- `2`: 暂停
- `3`: 完成
- `4`: 失败

## 鉴权说明

接口支持可选的imToken鉴权：
- 可以在请求头中添加 `Im-Token: your_token`
- 或在请求参数中添加 `imToken=your_token`
- 如果不提供token，则无需鉴权
- token通过 `/app-api/linban/user/login-im` 接口获取

## 使用示例

### JavaScript 前端示例

```javascript
// 1. 初始化上传
const initResponse = await fetch('/app-api/linban/upload/init', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Im-Token': 'your_token' // 可选
  },
  body: JSON.stringify({
    fileName: 'forest_video.mp4',
    totalSize: file.size,
    chunkSize: 5 * 1024 * 1024, // 5MB
    fileMd5: await calculateMD5(file),
    fileType: file.type,
    linbanId: 1,
    linbanName: '东山林班',
    collectTime: new Date().toISOString(),
    collector: '张三'
  })
});

const { uploadId, totalChunks } = initResponse.data;

// 2. 分片上传
for (let i = 0; i < totalChunks; i++) {
  const chunk = file.slice(i * chunkSize, (i + 1) * chunkSize);
  const chunkMd5 = await calculateMD5(chunk);
  
  const formData = new FormData();
  formData.append('uploadId', uploadId);
  formData.append('chunkIndex', i);
  formData.append('chunkFile', chunk);
  formData.append('chunkMd5', chunkMd5);
  
  await fetch('/app-api/linban/upload/chunk', {
    method: 'POST',
    body: formData
  });
}

// 3. 完成上传
await fetch('/app-api/linban/upload/complete', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ uploadId })
});
```

## 注意事项

1. 分片大小建议设置为 1-10MB
2. 系统会自动清理7天前的过期上传任务
3. 临时文件存储在服务器的临时目录中
4. 支持断点续传，相同文件MD5和大小会复用之前的上传任务
5. 所有接口都无需强制鉴权，但建议在生产环境中使用imToken
